package com.accolm.licenseManager.Entities;

import javax.enterprise.inject.New;
import javax.json.bind.annotation.JsonbProperty;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import org.eclipse.jdt.internal.compiler.ast.ThisReference;

import com.fasterxml.jackson.annotation.JsonProperty;

// A Company.
@Entity
@Table(name = "users")
public class User {

	@NotNull
	@Column(name = "FIRST_NAME")
	private String firstName;

	@NotNull
	@Column(name = "LAST_NAME")
	private String lastName;

	@Column(name = "COMPANY_ID")
	private Integer companyId;

	@NotNull
	@Column(name = "role")
	private String role;

	@NotNull
	@Id
	@Column(name = "USERNAME")
	@JsonbProperty("userName")
	private String username;

	@NotNull
	@Column(name = "EMAIL")
	private String email;

	@NotNull
	@Column(name = "PASSWORD")
	private String password;

	@NotNull
	@Column(name = "ENABLED")
	private boolean enabled;

	public User() {

	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Integer getCompanyId() {

		return companyId;
	}

	public void setCompanyId(Integer companyId) {

		this.companyId = companyId;

	}

	public @NotNull String getRole() {
		return role;
	}

	public void setRole(@NotNull String role) {
		this.role = role.toUpperCase(); // make role CAPS
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public @NotNull boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(@NotNull int enabled) {

		this.enabled = switch (enabled) {
		case 1 -> true;
		case 0 -> false;
		default -> false;
		};

	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;

	}

	public static void main(String[] args) {
		Integer integer = null;
		System.out.println(integer);
	}

}
