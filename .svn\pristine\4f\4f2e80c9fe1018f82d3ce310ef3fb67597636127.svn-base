package com.accolm.licenseManager.Entities;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

@Entity
@Table(name = "authorities")
public class Authority {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private int id;

	@NotNull
	@Column(name = "USERNAME")
	private String username;

	@NotNull
	@Column(name = "AUTHORITY")
	private String authority;

	public Authority() {
	}

	public Authority(String userRole) {
		this.authority = "ROLE_".concat(userRole); // concat role prefix, like ROLE_ADMIN . Spring Security
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getAuthority() {
		return authority;
	}

	public void setAuthority(String authority) {
		
		if(!authority.contains("ROLE_")) {
			this.authority = "ROLE_".concat(authority);
			return ;
		}
		
		this.authority = authority; // by default ROLE_ prefix is added on constructor call with role as paramater.
	}

	


}
