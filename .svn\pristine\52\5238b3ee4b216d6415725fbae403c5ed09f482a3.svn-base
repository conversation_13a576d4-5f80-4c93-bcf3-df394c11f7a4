package com.accolm.licenseManager.Services;

import java.util.List;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.DAO.CountryDAO;
import com.accolm.licenseManager.DAO.CountryDAOImpl;
import com.accolm.licenseManager.Entities.Country;

public class CountryServiceImpl implements CountryService {

	CountryDAO impl;

	Logger logger = LogManager.getLogger(LicenseServiceImpl.class);

	public CountryServiceImpl() {
		impl = new CountryDAOImpl();
	}

	@Override
	public List<Country> getAllCountries() {

		List<Country> countries = impl.getAllCountries();

		return countries;
	}

}
