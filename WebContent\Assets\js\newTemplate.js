var tempForm = Vue.component("temp-form", {
  data() {
    return {
      newTemp: {
        name: "",
        licenseExpiry: "",
        floatExpiry: 0,
        templateProperties: [{ name: "", value: "" }],
      },
      rowCount: 1,
      allowBtn: false,
      propName: "",
      propValue: "",
    };
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1]; // Gets the first segment after domain
      return `${baseUrl}/${projectName}/api`;
    },
  },
  methods: {
    async submitTemplate() {
      if (document.getElementById("gridRadios2").checked) {
        const strictDate = document.querySelector('input[type="date"]').value;
        this.newTemp.licenseExpiry = strictDate;
      } else if (document.getElementById("gridRadios3").checked) {
        this.newTemp.licenseExpiry = this.newTemp.licenseExpiry + " days";
      }

      // Debug: Log the original properties
      console.log(
        "Original template properties:",
        this.newTemp.templateProperties
      );

      // Filter out properties with empty names, but allow empty values
      const filteredProperties = this.newTemp.templateProperties
        .filter((prop) => prop.name && prop.name.trim() !== "")
        .map((prop) => ({
          ...prop,
          value:
            prop.value && prop.value.trim() !== "" ? prop.value.trim() : "", // Convert empty/null to empty string
        }));

      // Debug: Log the filtered properties
      console.log("Filtered properties:", filteredProperties);

      // Create a copy of the template with filtered properties
      const templateToSend = {
        ...this.newTemp,
        templateProperties: filteredProperties,
      };

      // Debug: Log the final template being sent
      console.log("Template being sent:", templateToSend);

      var myJSON = JSON.stringify(templateToSend);

      const response = await axios.post(
        `${this.apiBaseUrl}/templates/add`,
        myJSON,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data) {
        $("#onSaveModal").modal("show");
      } else {
        $("#onFailureModal").modal("show");
      }
    },
    reloadPage() {
      window.location.reload(true);
    },

    removeProp(row) {
      if (this.rowCount == 1) {
        console.log(this.rowCount);
        this.disableBtn = true;
      } else if (this.rowCount > 1) {
        console.log(this.rowCount);
        this.newTemp.templateProperties.splice(row, 1);
        this.rowCount--;
      }
    },
    addProp() {
      if (this.rowCount >= 1) {
        this.newTemp.templateProperties.push({
          name: "",
          value: "",
        });
        this.disableBtn = false;
        this.rowCount++;
      } else if (this.rowCount == 0) {
        this.newTemp.templateProperties.push({
          name: "",
          value: "",
        });
        this.rowCount++;
      }
    },
    reloadPage() {
      window.location.reload(true);
    },
  },
  template: `
    <div class="row">
      <div class="col-lg-12">
        <div class="card shadow mb-4">
          <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">New Template</h6>
          </div>
          <div class="card-body">
            <form class="form-horizontal">
              <div class="form-group row">
                <label class="col-sm-4 text-left control-label col-form-label">Template Name</label>
                <div class="col-sm-8">
                  <input autocomplete="off" v-model="newTemp.name" type="text" name="name" class="form-control" placeholder="Template Name">
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-4 text-left control-label col-form-label">License Expiration</label>
                <div class="col-sm-8">
                  <input class="form-check-input" type="radio" name="gridRadios" id="gridRadios1" value="option1" checked>
                  <label class="form-check-label" for="gridRadios1">None</label>
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-4 text-left control-label col-form-label"></label>
                <div class="col-sm-1">
                  <input class="form-check-input" type="radio" name="gridRadios" id="gridRadios2" value="option2">
                  <label class="form-check-label" for="gridRadios2">Strict</label>
                </div>
                <div class="col-sm-3">
                  <input type="date" class="form-control">
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-4 text-left control-label col-form-label"></label>
                <div class="col-sm-1">
                  <input class="form-check-input" type="radio" name="gridRadios" id="gridRadios3" value="option3">
                  <label class="form-check-label" for="gridRadios3">Delta</label>
                </div>
                <div class="col-sm-2">
                  <input autocomplete="off" v-model="newTemp.licenseExpiry" type="text" class="form-control">
                </div>
                <div class="col-sm-3">
                  <strong>days from creation</strong>
                </div>
              </div>
              <div class="form-group row">
                <label class="col-sm-4 text-left control-label col-form-label">Floating Expiration</label>
                <div class="col-sm-1"></div>
                <div class="col-sm-2">
                  <input v-model="newTemp.floatExpiry" type="number" class="form-control">
                </div>
                <div class="col-sm-4 mt-1">
                  <strong>days</strong>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-lg-12">
          <div class="card shadow mb-4">
            <div class="card-header py-3">
              <h6 class="m-0 font-weight-bold text-primary">Template Properties</h6>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-sm-3"></div>
                <table class="table table-hover table-bordered border-dark col-sm-12 text-center" style="width: 100% !important;">
                  <thead>
                    <tr class="table-secondary">
                      <th scope="col">Property</th>
                      <th scope="col">Value</th>
                      <th scope="col"></th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(property, index) in newTemp.templateProperties">
                      <td>
                        <input v-model="newTemp.templateProperties[index].name" type="text" class="form-control" placeholder="<add a property>">
                      </td>
                      <td>
                        <input v-model="newTemp.templateProperties[index].value" type="text" class="form-control" placeholder="<add a value>">
                      </td>
                      <td>
                        <div class="btn-group">
                          <button :disabled='allowBtn' type="button" class="btn btn-danger btn-sm" @click="removeProp(index)" title="Remove Property">
                            <i class="fas fa-trash-alt"></i>
                          </button>
                          <button type="button" class="btn btn-primary btn-sm" @click='addProp()' title="Add Property">
                            <i class="fa fa-plus-square" aria-hidden="true"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <hr style="background-color:black; padding: 0 1px;">
              </div>
              <br>
              <div class="border-top pull-right">
                <div class="card-body">
                  <button v-on:click.prevent="submitTemplate" type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Template & Properties
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

     <!-- On Success Modal-->
<div class="modal fade" id="onSaveModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onSaveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body text-center">
                <i class="fa fa-check bg-success align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                <p class="lead text-success mb-5">
                    <strong>Template Saved Successfully...</strong>
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" @click="reloadPage()">Close</button>
            </div>
        </div>
    </div>
</div>



      <!-- On Failure Modal-->
      <div class="modal fade" id="onFailureModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onFailureModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-body text-center">
              <i class="fa fa-times bg-danger align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
              <p class="lead text-danger mb-5">
                <strong>Failed to Save Template....</strong>
              </p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>

    </div>
  `,
});

new Vue({
  el: "#tempApp",
  components: {
    "temp-form": tempForm,
  },
});
