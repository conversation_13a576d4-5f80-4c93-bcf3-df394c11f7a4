package com.accolm.licenseManager.Utils;

import java.util.Random;


public class LicenseKeyGeneratorUtility {

	private final static String CHARACTERS = "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
	private static String keyFormat = "xxxx-xxxx-xxxx-xxxx";

	public static String generateLicenseKey() {

		StringBuilder keyBuilder = new StringBuilder(); // keysize of 16

		String key = keyBuilder.toString();

		String parts = "";

		for (int i = 0, partSize = 0; i < CHARACTERS.length(); i++) {
			parts = parts.concat(String.valueOf(CHARACTERS.charAt(new Random().nextInt(0, CHARACTERS.length()))));
			if (parts.toString().length() == 4) {
				keyBuilder.append(parts);
				keyBuilder.append("-");
				partSize++;// size of blocks
				if (partSize == 4) {
					key = keyBuilder.toString().substring(0, keyFormat.length());
				}
				parts = "";

			}

		}

		return key;
	}

	public static void main(String[] args) {

		System.out.println(generateLicenseKey());

	}

}
