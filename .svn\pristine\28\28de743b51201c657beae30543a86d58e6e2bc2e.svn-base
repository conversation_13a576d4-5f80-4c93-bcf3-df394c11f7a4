package com.accolm.licenseManager.RestInterface;

import java.util.List;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.accolm.licenseManager.Entities.Contact;
import com.accolm.licenseManager.Entities.License;
import com.accolm.licenseManager.Entities.LicenseProperty;
import com.accolm.licenseManager.Services.LicenseServiceImpl;

@Path("licenses")
public class LicenseRI {

    private LicenseServiceImpl impl = new LicenseServiceImpl();

    // Retrieve all licenses
    @GET
    @Path("all")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAllLicenses() {
        List<License> licenses = impl.getAllLicenses();
        if (licenses == null || licenses.isEmpty()) {
            return Response.status(Response.Status.NO_CONTENT).entity("No licenses found.").build();
        }
        return Response.ok(licenses).build();
    }

    // Create a new license
    @POST
    @Path("create")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response createLicense(License license) {
    	
       //What you need to do is send an empty expiration date to the back-end when the expiration is Never.
        String result = impl.createLicense(license);
        if ("Invalid license data provided.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.status(Response.Status.CREATED).entity(result).build();
    }

    // Retrieve a license by ID
    @GET
    @Path("get/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getLicense(@PathParam("id") int id) {
        License license = impl.getLicense(id);
        if (license == null || license.getId() <= 0) {
            return Response.status(Response.Status.NOT_FOUND).entity("License not found.").build();
        }
        return Response.ok(license).build();
    }

    // Delete a license by ID
    @DELETE
    @Path("delete/{id}")
    @Produces(MediaType.TEXT_PLAIN)
    public Response deleteLicense(@PathParam("id") int id) {
        String result = impl.deleteLicense(id);
        if ("ID passed does not exist.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.ok(result).build();
    }

    // Update a license
    @PUT
    @Path("update")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response updateLicense(License license) {
        String result = impl.updateLicense(license);
        if ("Invalid license data provided.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.ok(result).build();
    }
    
    
    // edit a license
    @PUT
    @Path("edit")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response editLicense(License license) {
        String result = impl.updateLicense(license);
        if ("Invalid license data provided.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.ok(result).build();
    }

    // Count total licenses
    @GET
    @Path("count")
    @Produces(MediaType.TEXT_PLAIN)
    public Response licenseCount() {
        long count = impl.getLicenseCount();
        return Response.ok(count).build();
    }

    // Download license file
    @GET
    @Path("download/{id}")
    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    public Response downloadFile(@PathParam("id") int id) {
        return impl.downloadLicense(id);
    }

    // Search licenses by input
    @GET
    @Path("search/{input}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response searchLicense(@PathParam("input") String input) {
        List<License> licenses = impl.getLicenses(input);
        if (licenses == null || licenses.isEmpty()) {
            return Response.status(Response.Status.NOT_FOUND).entity("No licenses found for the given input.").build();
        }
        return Response.ok(licenses).build();
    }

    // Paginate licenses
//    @GET
//    @Path("pagination/{page}")
//    @Produces(MediaType.APPLICATION_JSON)
//    public Response licensePagination(@PathParam("page") int page) {
//        List<License> licenses = impl.getLicensePage(page);
//        if (licenses == null || licenses.isEmpty()) {
//            return Response.status(Response.Status.NO_CONTENT).entity("No licenses found for the requested page.").build();
//        }
//        return Response.ok(licenses).build();
//    }
    
    @GET
    @Path("pagination/{page}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response licensePagination(@PathParam("page") int page) {
        List<License> licenses = impl.getLicensePage(-1);
        if (licenses == null || licenses.isEmpty()) {
            return Response.status(Response.Status.NO_CONTENT).entity("No licenses found for the requested page.").build();
        }
        return Response.ok(licenses).build();
    }


    // Validate license status
    @GET
    @Path("validate/{id}")
    @Produces(MediaType.TEXT_PLAIN)
    public Response validateLicenseStatus(@PathParam("id") int id) {
        String result = impl.validateLicenseStatus(id);
        if (result.contains("not found")) {
            return Response.status(Response.Status.NOT_FOUND).entity(result).build();
        }
        return Response.ok(result).build();
    }

    // Create a license from a template
    @POST
    @Path("create-from-template/{templateId}")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response createLicenseFromTemplate(@PathParam("templateId") int templateId, License license) {
        String result = impl.createLicenseFromTemplate(templateId, license);
        if (result.contains("not found")) {
            return Response.status(Response.Status.NOT_FOUND).entity(result).build();
        }
        return Response.ok(result).build();
    }

    // Populate template properties into license properties table
    @GET
    @Path("populate-properties/{id}")
    @Produces(MediaType.APPLICATION_JSON)
    public Response populateLicenseProperties(@PathParam("id") int licenseId) {
        List<LicenseProperty> properties = impl.populateLicenseProperties(licenseId);
        if (properties == null || properties.isEmpty()) {
            return Response.status(Response.Status.NO_CONTENT).entity("No properties found for the license.").build();
        }
        return Response.ok(properties).build();
    }

    // Save multiple license properties
    @POST
    @Path("properties/save")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response saveLicenseProperties(List<LicenseProperty> properties) {
        String result = impl.saveLicenseProperties(properties);
        if ("Invalid properties.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.status(Response.Status.CREATED).entity(result).build();
    }

    // Retrieve all license properties
    @GET
    @Path("properties/all")
    @Produces(MediaType.APPLICATION_JSON)
    public Response getAllLicenseProperties() {
        List<LicenseProperty> properties = impl.getAllLicenseProperties();
        if (properties == null || properties.isEmpty()) {
            return Response.status(Response.Status.NO_CONTENT).entity("No license properties found.").build();
        }
        return Response.ok(properties).build();
    }

    // Update license property
    @PUT
    @Path("properties/update")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.TEXT_PLAIN)
    public Response updateLicenseProperty(LicenseProperty property) {
        String result = impl.updateLicenseProperty(property);
        if ("Invalid property data provided.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.ok(result).build();
    }

    // Delete license property
    @DELETE
    @Path("properties/delete/{id}")
    @Produces(MediaType.TEXT_PLAIN)
    public Response deleteLicenseProperty(@PathParam("id") int id) {
        String result = impl.deleteLicenseProperty(id);
        if ("Property ID does not exist.".equals(result)) {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
        return Response.ok(result).build();
    }

 // Calculate license expiration date
    @GET
    @Path("calculate-expiry/{id}")
    @Produces(MediaType.TEXT_PLAIN)
    public Response calculateLicenseExpiry(@PathParam("id") int id) {
        String result = impl.calculateLicenseExpiry(id);
        if (result == null) {
            return Response.status(Response.Status.BAD_REQUEST).entity("Error calculating expiration date.").build();
        }
        return Response.ok(result).build();
    }
    
 // 
 	@GET
 	@Consumes(MediaType.APPLICATION_JSON)
 	@Produces(MediaType.APPLICATION_JSON)
 	@Path("company/{companyId}")
 	public Response produceLicensesBelongingToCompany(@PathParam("companyId") int companyId) {
 		List<License> result = impl.getLicensesBelongingToCompany(companyId);
 		return Response.status(Response.Status.OK).entity(result).build();
 	}
 	
}
