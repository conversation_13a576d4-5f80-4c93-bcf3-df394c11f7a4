package com.accolm.licenseManager.RestInterface;

import java.io.BufferedReader;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import org.glassfish.jersey.media.multipart.FormDataContentDisposition;
import org.glassfish.jersey.media.multipart.FormDataParam;

import com.accolm.licenseManager.Entities.Template;
import com.accolm.licenseManager.Entities.TemplateProperty;
import com.accolm.licenseManager.Services.TemplateService;

@Path("templates")
public class TemplateRI {

	// Instance of the TemplateService
	private final TemplateService service = new TemplateService();

	// Retrieve all templates
	@GET
	@Path("all") // Added missing path
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTemplates() {
		List<Template> templates = service.getTemplates();
		if (templates == null || templates.isEmpty()) {
			return Response.status(Response.Status.NO_CONTENT).entity("No templates found.").build();
		}
		return Response.ok(templates).build();
	}

	// Add a new template
	@POST // POST
	@Path("add")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.TEXT_PLAIN)
	public Response addTemplate(Template template) {

		// if the template license is floating expiration.
		// set floating.
		// else if template license is fixed expiration.
		// set fixed expiration.
		String result = service.addTemplate(template); 	//old api saves floating days and fixed dates.
		if ("ALLGOOD".equals(result)) {
			return Response.status(Response.Status.CREATED).entity("Template added successfully.").build();
		}
		return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
	}

	// Add a new template property
	@POST
	@Path("property/add")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.TEXT_PLAIN)
	public Response addTemplateProperty(TemplateProperty property) {
		String result = service.addTemplateProperty(property);
		if ("ALLGOOD".equals(result)) {
			return Response.status(Response.Status.CREATED).entity("Template property added successfully.").build();
		}
		return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
	}

	// Retrieve all template properties
	@GET
	@Path("properties/all")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllTemplateProperties() {
		List<TemplateProperty> properties = service.getAllTemplateProperties();
		if (properties == null || properties.isEmpty()) {
			return Response.status(Response.Status.NO_CONTENT).entity("No template properties found.").build();
		}
		return Response.ok(properties).build();
	}

	// Update an existing template
	@PUT
	@Path("update")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.TEXT_PLAIN)
	public Response updateTemplate(Template template) {
		String result = service.updateTemplate(template);
		if ("ALLGOOD".equals(result)) {
			return Response.ok("Template updated successfully.").build();
		}
		return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
	}

	// Update an existing template property
	@PUT
	@Path("property/update")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.TEXT_PLAIN)
	public Response updateTemplateProperty(TemplateProperty property) {
		String result = service.updateTemplateProperty(property);
		if ("ALLGOOD".equals(result)) {
			return Response.ok("Template property updated successfully.").build();
		}
		return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
	}

	// Delete a template by ID
	@DELETE
	@Path("delete/{id}")
	@Produces(MediaType.TEXT_PLAIN)
	public Response deleteTemplate(@PathParam("id") int id) {
		String result = service.deleteTemplate(id);
		if ("ALLGOOD".equals(result)) {
			return Response.ok("Template deleted successfully.").build();
		}
		return Response.status(Response.Status.NOT_FOUND).entity(result).build();
	}

	// Delete a template property by ID
	@DELETE
	@Path("property/delete/{id}")
	@Produces(MediaType.TEXT_PLAIN)
	public Response deleteTemplateProperty(@PathParam("id") int id) {
		String result = service.deleteTemplateProperty(id);
		if ("ALLGOOD".equals(result)) {
			return Response.ok("Template property deleted successfully.").build();
		}
		return Response.status(Response.Status.NOT_FOUND).entity(result).build();
	}

	// Count the total number of templates
	@GET
	@Path("count")
	@Produces(MediaType.TEXT_PLAIN)
	public Response countTemplates() {
		long count = service.countTemplates();
		return Response.ok(count).build();
	}

	// Retrieve a specific template by ID
	@GET
	@Path("get/{id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTemplate(@PathParam("id") int id) {
		Template template = service.getTemplate(id);
		if (template == null || template.getId() <= 0) {
			return Response.status(Response.Status.NOT_FOUND).entity("Template with ID " + id + " not found.").build();
		}
		return Response.ok(template).build();
	}

	// Search templates by input
	@GET
	@Path("search/{input}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response searchTemplate(@PathParam("input") String input) {
		List<Template> templates = service.searchTemplates(input);
		if (templates == null || templates.isEmpty()) {
			return Response.status(Response.Status.NOT_FOUND).entity("No templates found for input: " + input).build();
		}
		return Response.ok(templates).build();
	}

	// Paginate templates
	@GET
	@Path("pagination/{page}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response paginateTemplates(@PathParam("page") int page) {
		if (page <= 0) { // Ensure the page number is valid (1-based indexing assumed)
			return Response.status(Response.Status.BAD_REQUEST).entity("Page number must be greater than 0.").build();
		}

		List<Template> templates = service.pagination(page);

		// Handle empty list
		if (templates == null || templates.isEmpty()) {
			return Response.status(Response.Status.NO_CONTENT).entity("No templates found for the requested page.")
					.build();
		}

		return Response.ok(templates).build();
	}

	// import template file
	// Call Endpoint.
	// do file upload.
	// save file to the database.
	// respond with success.

	// multipart/form-data
	/*
	 * Content-Type: multipart/form-data; boundary=aBoundaryString (other headers
	 * associated with the multipart document as a whole)
	 * 
	 * --aBoundaryString Content-Disposition: form-data; name="myFile";
	 * filename="img.jpg" Content-Type: image/jpeg
	 * 
	 * (data) --aBoundaryString Content-Disposition: form-data; name="myField"
	 * 
	 * (data) --aBoundaryString (more subparts) --aBoundaryString--
	 * 
	 */


	
	
	@POST
	@Path("file/import")
	@Consumes(MediaType.MULTIPART_FORM_DATA)
	@Produces(MediaType.TEXT_PLAIN)
	public Response importTemplateFile(@FormDataParam("file") InputStream path,
			@FormDataParam("file") FormDataContentDisposition fileDetail) {

		if (path == null || fileDetail == null) {
			return Response.status(Response.Status.BAD_REQUEST).entity("No file uploaded.").build();
		}

		// Validate file extension to ensure it's a signing key file
		String fileName = fileDetail.getFileName();
		if (!fileName.endsWith(".ltp")) {
			return Response.status(Response.Status.BAD_REQUEST)
					.entity("Invalid file type. Only .ltp files are allowed.").build();
		}

		try (BufferedReader br = new BufferedReader(new InputStreamReader(path))) {
			Template template = new Template();
			String line;
			StringBuffer content = new StringBuffer();
			// read contents from the imported template file.
			while ((line = br.readLine()) != null) {
				// create a template object.
				if (!line.startsWith("#")) {

					// get template properties, name
					if (line.startsWith("name=")) {
						// name=demo
						template.setName(line.substring("name=".length()));
					}

					// get template properties, licenseExpiry
					if (line.startsWith("licenseExpiry=")) {
						template.setLicenseExpiry(line.substring("licenseExpiry=".length()));

					}

					// get template properties, floatingExpiry
					if (line.startsWith("floatingExpiry=")) {
						template.setFloatExpiry(Integer.parseInt(line.substring("floatingExpiry=".length())));
					}

				}

			}

			// save the template to the template table.
			service.addTemplate(template);

		} catch (IOException e) {
			return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
					.entity("Error saving uploaded file: " + e.getMessage()).build();
		}

		return Response.status(Response.Status.OK)
				.entity("Template file, {FILE_NAME} ,successfully uploaded".replace("{FILE_NAME}", fileName)).build();

	}

}
