package com.accolm.licenseManager.Entities;

import javax.json.bind.annotation.JsonbPropertyOrder;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;

import org.apache.commons.lang.BooleanUtils;
import org.springframework.lang.NonNull;

// A Maintenance-Support Request Form.
@Entity
@Table(name = "maintenance_supports")
// order as database table.
@JsonbPropertyOrder(value = { "id", "companyId", "licenseId", "licenseKey", "product", "productVersion", "licenseType",
		"startDate", "endDate", "status", "extendedSupport", })
public class MaintenanceSupport {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ms_id")
	private int id;

	@NotNull
	@Column(name = "companyId")
	private int companyId;

	@NotNull
	@Column(name = "licenseKey")
	private String licenseKey;

	@NotNull
	@Column(name = "licenseId")
	private int licenseId;

	@Column(name = "product")
	private String product;

	public int getLicenseId() {
		return licenseId;
	}

	public void setLicenseId(int licenseId) {
		this.licenseId = licenseId;
	}

	@Column(name = "productVersion")
	private String productVersion;

	@Column(name = "licenseType")
	private String licenseType;

	@NotNull
	@Column(name = "startDate")
	private String startDate;

	@NotNull
	@Column(name = "endDate")
	private String endDate;

	@NotNull
	@Column(name = "status")
	private String status; //

	@NotNull
	@Column(name = "extendedSupport")
	private boolean extendedSupport;

	public MaintenanceSupport() {

	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public int getCompanyId() {
		return companyId;
	}

	public void setCompanyId(int companyId) {
		this.companyId = companyId;
	}

	public String getLicenseKey() {
		return licenseKey;
	}

	public void setLicenseKey(String licenseKey) {
		this.licenseKey = licenseKey;
	}

	public String getProduct() {
		return product;
	}

	public void setProduct(String product) {
		this.product = product;
	}

	public String getProductVersion() {
		return productVersion;
	}

	public void setProductVersion(String productVersion) {
		this.productVersion = productVersion;
	}

	public String getLicenseType() {
		return licenseType;
	}

	public void setLicenseType(String licenseType) {
		this.licenseType = licenseType;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public boolean isExtendedSupport() {
		return extendedSupport;
	}

	/*public void setExtendedSupport(int extendedSupport) {
		this.extendedSupport = switch (extendedSupport) {
		case 1 -> true;
		case 0 -> false;
		default -> false;
		};

	}*/
	
	public void setExtendedSupport(boolean extendedSupport) {
	    this.extendedSupport = extendedSupport;
	}


	@Override
	public String toString() {
		return "MaintenanceSupport [id=" + id + ", companyId=" + companyId + ", licenseKey=" + licenseKey
				+ ", licenseId=" + licenseId + ", product=" + product + ", productVersion=" + productVersion
				+ ", licenseType=" + licenseType + ", startDate=" + startDate + ", endDate=" + endDate + ", status="
				+ status + ", extendedSupport=" + extendedSupport + "]";
	}

}
