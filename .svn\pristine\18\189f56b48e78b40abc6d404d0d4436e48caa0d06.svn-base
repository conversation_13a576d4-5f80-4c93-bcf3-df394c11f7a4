// PaginationStrip component
var PaginationStrip = Vue.component("pagination-strip", {
  props: ["currentPage", "pages", "maxPerPage", "itemCount"],
  methods: {
    async onChange(event) {
      const numberToGet = parseInt(event.target.value);
      this.$emit("update:maxPerPage", numberToGet);
      // Reset to page 1 when changing results per page
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onNext() {
      if (this.currentPage < this.pages) {
        this.$emit("update:currentPage", this.currentPage + 1);
        this.$emit("refresh");
      }
    },
    async onPrev() {
      if (this.currentPage > 1) {
        this.$emit("update:currentPage", this.currentPage - 1);
        this.$emit("refresh");
      }
    },
    async onFirst() {
      this.$emit("update:currentPage", 1);
      this.$emit("refresh");
    },
    async onLast() {
      this.$emit("update:currentPage", this.pages);
      this.$emit("refresh");
    },
  },
  template: `
       <th class="tableHeader" colspan="10" scope="colgroup" style="color: #000000; font-family: 'Open Sans', sans-serif; border: none;">
        <div class="pull-left" style="line-height: 34px;">Count: {{itemCount}}</div>
        <div class="pull-right" style="display: flex; align-items: center; gap: 10px;">
          <span>Page {{currentPage}} of {{pages}}</span>
          <ul class="pagination pagination-sm" style="margin: 0; border: none;">
            <li :class="{disabled: currentPage === 1}" style="border: none;">
              <span @click="onFirst()" style="border: none; cursor: pointer;">
                <i class="glyphicon glyphicon-step-backward"></i>
              </span>
            </li>
            <li :class="{disabled: currentPage === 1}" style="border: none;">
              <span @click="onPrev()" style="border: none; cursor: pointer;">
                <i class="glyphicon glyphicon-chevron-left"></i>
              </span>
            </li>
            <li :class="{disabled: currentPage === pages}" style="border: none;">
              <span @click="onNext()" style="border: none; cursor: pointer;">
                <i class="glyphicon glyphicon-chevron-right"></i>
              </span>
            </li>
            <li :class="{disabled: currentPage === pages}" style="border: none;">
              <span @click="onLast()" style="border: none; cursor: pointer;">
                <i class="glyphicon glyphicon-step-forward"></i>
              </span>
            </li>
          </ul>
          <span style="white-space: nowrap;">Results per page : </span>
          <select class="pages-form-control" @change="onChange($event)" style="margin-left: 5px;">
            <option value="10" :selected="maxPerPage === 10">10</option>
            <option value="25" :selected="maxPerPage === 25">25</option>
            <option value="50" :selected="maxPerPage === 50">50</option>
            <option value="100" :selected="maxPerPage === 100">100</option>
          </select>
        </div>
      </th>
    `,
});

// Autocomplete component
Vue.component("company-autocomplete", {
  props: ["companies", "value"],
  data() {
    return {
      searchText: "",
      showDropdown: false,
      filteredCompanies: [],
      selectedCompany: null,
      highlightedIndex: -1,
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          // Find the company by ID and set the search text
          const company = this.companies.find((c) => c.id === parseInt(newVal));
          if (company) {
            this.searchText = company.name;
            this.selectedCompany = company;
          }
        } else {
          this.searchText = "";
          this.selectedCompany = null;
        }
      },
    },
    searchText(newVal) {
      if (!newVal) {
        this.selectedCompany = null;
        this.$emit("input", "");
      }

      // Filter companies based on search text
      this.filterCompanies();
    },
  },
  methods: {
    filterCompanies() {
      if (!this.searchText) {
        this.filteredCompanies = [];
        this.showDropdown = false;
        return;
      }

      const searchTerm = this.searchText.toLowerCase();
      this.filteredCompanies = this.companies.filter((company) =>
        company.name.toLowerCase().includes(searchTerm)
      );

      this.showDropdown = this.filteredCompanies.length > 0;
      this.highlightedIndex = -1;
    },
    selectCompany(company) {
      this.selectedCompany = company;
      this.searchText = company.name;
      this.$emit("input", company.id);
      this.showDropdown = false;
    },
    onFocus() {
      if (this.searchText) {
        this.filterCompanies();
      }
    },
    onBlur() {
      // Delay hiding dropdown to allow click events to register
      setTimeout(() => {
        this.showDropdown = false;

        // If there's text but no selection, reset
        if (this.searchText && !this.selectedCompany) {
          const matchingCompany = this.companies.find(
            (c) => c.name.toLowerCase() === this.searchText.toLowerCase()
          );

          if (matchingCompany) {
            this.selectCompany(matchingCompany);
          } else {
            this.searchText = this.selectedCompany
              ? this.selectedCompany.name
              : "";
          }
        }
      }, 200);
    },
    onKeyDown(e) {
      if (!this.showDropdown) return;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          this.highlightedIndex = Math.min(
            this.highlightedIndex + 1,
            this.filteredCompanies.length - 1
          );
          this.scrollToHighlighted();
          break;
        case "ArrowUp":
          e.preventDefault();
          this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
          this.scrollToHighlighted();
          break;
        case "Enter":
          e.preventDefault();
          if (this.highlightedIndex >= 0) {
            this.selectCompany(this.filteredCompanies[this.highlightedIndex]);
          }
          break;
        case "Escape":
          e.preventDefault();
          this.showDropdown = false;
          break;
      }
    },
    scrollToHighlighted() {
      this.$nextTick(() => {
        const highlighted = this.$el.querySelector(".highlighted");
        if (highlighted) {
          highlighted.scrollIntoView({
            block: "nearest",
            behavior: "smooth",
          });
        }
      });
    },
    clearSelection() {
      this.searchText = "";
      this.selectedCompany = null;
      this.$emit("input", "");
    },
  },
  template: `
      <div class="company-autocomplete-container" style="position: relative; width: 100%;">
        <div class="input-group" style="width: 56%;">
          <input
            type="text"
            class="form-control"
            v-model="searchText"
            @focus="onFocus"
            @blur="onBlur"
            @keydown="onKeyDown"
            placeholder="Search for a company..."
            style="width: calc(100% - 34px); border-radius: 4px 0 0 4px;"
          >
          <div class="input-group-btn" style="width: 34px; position: absolute; right: 0; top: 0; height: 100%;">
            <button 
              v-if="searchText" 
              class="btn btn-default" 
              type="button" 
              @click="clearSelection"
              style="height: 100%; border-radius: 0 4px 4px 0;"
            >
              <i class="glyphicon glyphicon-remove"></i>
            </button>
            <button 
              v-else 
              class="btn btn-default" 
              type="button"
              style="height: 100%; border-radius: 0 4px 4px 0; cursor: default;"
              disabled
            >
              <i class="glyphicon glyphicon-search"></i>
            </button>
          </div>
        </div>
        <div 
          v-show="showDropdown" 
          class="dropdown-menu" 
          style="display: block; width: 56%; max-height: 300px; overflow-y: auto; position: absolute; z-index: 1000;"
        >
          <a 
            v-for="(company, index) in filteredCompanies" 
            :key="company.id"
            href="#" 
            class="dropdown-item" 
            :class="{ 'highlighted': index === highlightedIndex }"
            @mousedown.prevent="selectCompany(company)"
            @mouseover="highlightedIndex = index"
            style="display: block; padding: 8px 15px; text-decoration: none; color: #333;"
          >
            {{ company.name }}
          </a>
        </div>
      </div>
    `,
});

// display-contacts component
Vue.component("display-contacts", {
  components: {
    "pagination-strip": PaginationStrip,
    "company-autocomplete": Vue.component("company-autocomplete"),
  },
  data() {
    return {
      contacts: [],
      allContacts: [], // Store all contacts for filtering
      companies: [], // For company dropdown
      isFetching: false,
      currentPage: 1,
      maxPerPage: 10,
      itemCount: 0,
      pages: 1,
      activeTab: "list",
      contactData: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
        companyId: null,
      },
      validationErrors: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
        companyId: "",
      },
      editContactData: {
        id: null,
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
        companyId: null,
      },
      editValidationErrors: {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        contactType: "",
        companyId: "",
      },
      deleteContactId: null,
      filters: {
        firstName: "",
        lastName: "",
        company: "",
        email: "",
        contactType: "",
      },
      contactTypeLabels: {
        primary: "Primary",
        secondary: "Secondary",
        technical: "Technical",
        billing: "Billing",
        // lowercase versions in case the API returns lowercase values
        PRIMARY: "Primary",
        SECONDARY: "Secondary",
        TECHNICAL: "Technical",
        BILLING: "Billing",
      },
    };
  },

  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },

    filteredContacts() {
      if (!this.allContacts || !this.allContacts.length) {
        return this.contacts;
      }

      let filtered = [...this.allContacts];

      // Filter by first name
      if (this.filters.firstName) {
        const searchTerm = this.filters.firstName.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.firstName &&
            contact.firstName.toLowerCase().includes(searchTerm)
        );
      }

      // Filter by last name
      if (this.filters.lastName) {
        const searchTerm = this.filters.lastName.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.lastName &&
            contact.lastName.toLowerCase().includes(searchTerm)
        );
      }

      // Filter by company
      if (this.filters.company) {
        filtered = filtered.filter(
          (contact) => contact.companyId === parseInt(this.filters.company)
        );
      }

      // Filter by contact type
      if (this.filters.contactType) {
        filtered = filtered.filter((contact) => {
          // Handle different possible formats of contactType
          if (typeof contact.contactType === "string") {
            return contact.contactType === this.filters.contactType;
          } else if (
            typeof contact.contactType === "object" &&
            contact.contactType !== null
          ) {
            if (contact.contactType.type) {
              return contact.contactType.type === this.filters.contactType;
            } else if (contact.contactType.name) {
              return contact.contactType.name === this.filters.contactType;
            }
          }
          return false;
        });
      }

      // Filter by email
      if (this.filters.email) {
        const searchTerm = this.filters.email.toLowerCase();
        filtered = filtered.filter(
          (contact) =>
            contact.email && contact.email.toLowerCase().includes(searchTerm)
        );
      }

      return filtered;
    },

    paginatedContacts() {
      const startIndex = (this.currentPage - 1) * this.maxPerPage;
      const endIndex = startIndex + this.maxPerPage;
      return this.filteredContacts.slice(startIndex, endIndex);
    },
  },

  methods: {
    async fetchContacts() {
      this.isFetching = true;
      try {
        const response = await axios.get(`${this.apiBaseUrl}/contacts/all`);
        if (response.status === 200) {
          this.allContacts = response.data;
          this.contacts = [...this.allContacts];
          this.itemCount = this.allContacts.length;
          this.pages = Math.ceil(this.itemCount / this.maxPerPage);
        }
      } catch (error) {
        console.error("Error fetching contacts:", error);
      } finally {
        this.isFetching = false;
      }
    },

    async fetchCompanies() {
      try {
        const response = await axios.get(`${this.apiBaseUrl}/companies`);
        if (response.status === 200) {
          this.companies = response.data;
        }
      } catch (error) {
        console.error("Error fetching companies:", error);
      }
    },

    setActiveTab(tab) {
      this.activeTab = tab;
    },

    formatContactType(type) {
      if (type == null) return "Unknown";
      if (typeof type === "string" && this.contactTypeLabels[type]) {
        return this.contactTypeLabels[type];
      }
      if (typeof type === "object") {
        if (type.name) return type.name;
        if (type.type && this.contactTypeLabels[type.type])
          return this.contactTypeLabels[type.type];
      }
      return String(type);
    },

    getCompanyName(companyId) {
      const company = this.companies.find((c) => c.id === companyId);
      return company ? company.name : "Unknown";
    },

    validateContactForm(isEdit = false) {
      let isValid = true;
      const data = isEdit ? this.editContactData : this.contactData;
      const errors = isEdit ? this.editValidationErrors : this.validationErrors;

      // Reset validation errors
      for (let key in errors) {
        errors[key] = "";
      }

      // First Name validation
      if (!data.firstName.trim()) {
        errors.firstName = "First name is required";
        isValid = false;
      } else if (data.firstName.length < 2) {
        errors.firstName = "First name must be at least 2 characters";
        isValid = false;
      }

      // Last Name validation
      if (!data.lastName.trim()) {
        errors.lastName = "Last name is required";
        isValid = false;
      } else if (data.lastName.length < 2) {
        errors.lastName = "Last name must be at least 2 characters";
        isValid = false;
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!data.email.trim()) {
        errors.email = "Email is required";
        isValid = false;
      } else if (!emailRegex.test(data.email)) {
        errors.email = "Please enter a valid email address";
        isValid = false;
      }

      // Phone validation (optional but must be valid if provided)
      const phoneRegex =
        /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/;
      if (data.phone.trim() && !phoneRegex.test(data.phone)) {
        errors.phone = "Please enter a valid phone number";
        isValid = false;
      }

      // Contact Type validation
      if (!data.contactType) {
        errors.contactType = "Please select a contact type";
        isValid = false;
      }

      // Company validation
      if (!data.companyId) {
        errors.companyId = "Please select a company";
        isValid = false;
      }

      return isValid;
    },

    resetContactForm() {
      this.contactData = {
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        mobilePhone: "",
        contactType: "",
        companyId: null,
      };
    },

    submitContact() {
      if (!this.validateContactForm()) {
        return; // Stop submission if validation fails
      }

      axios
        .post(`${this.apiBaseUrl}/contacts`, this.contactData)
        .then((response) => {
          if (response.status === 201) {
            this.activeTab = "list";
            this.fetchContacts();
            this.resetContactForm();

            // Show success modal
            $("#contactSuccessModal").modal("show");

            // Auto-hide the modal after 2 seconds
            setTimeout(() => {
              $("#contactSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error adding contact:", error);

          // Show error modal
          $("#contactErrorModal").modal("show");

          // Auto-hide the modal after 2 seconds
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },

    editContact(contactId) {
      const contact = this.contacts.find((c) => c.id === contactId);
      if (contact) {
        // Clone the contact data to avoid direct mutation
        this.editContactData = { ...contact };
        // Show the edit modal
        $("#editContactModal").modal("show");
      }
    },

    updateContact() {
      if (!this.validateContactForm(true)) {
        return; // Stop submission if validation fails
      }

      axios
        .put(`${this.apiBaseUrl}/contacts`, this.editContactData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          if (response.status === 200) {
            // Close the edit modal
            $("#editContactModal").modal("hide");

            // Refresh the contacts list
            this.fetchContacts();

            // Show success modal
            $("#contactUpdateSuccessModal").modal("show");

            // Auto-hide the modal after 2 seconds
            setTimeout(() => {
              $("#contactUpdateSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error updating contact:", error);

          // Close the edit modal
          $("#editContactModal").modal("hide");

          // Show error modal
          $("#contactErrorModal").modal("show");

          // Auto-hide the modal after 2 seconds
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },

    deleteModal(contactId) {
      this.deleteContactId = contactId;
      $("#deleteContactModal").modal("show");
    },

    deleteContact() {
      if (!this.deleteContactId) return;

      axios
        .delete(`${this.apiBaseUrl}/contacts/delete/${this.deleteContactId}`)
        .then((response) => {
          if (response.status === 200) {
            $("#deleteContactModal").modal("hide");

            // Remove the deleted contact from the local array
            this.allContacts = this.allContacts.filter(
              (contact) => contact.id !== this.deleteContactId
            );
            this.contacts = this.contacts.filter(
              (contact) => contact.id !== this.deleteContactId
            );

            // Show success modal
            $("#contactDeleteSuccessModal").modal("show");
            setTimeout(() => {
              $("#contactDeleteSuccessModal").modal("hide");
            }, 2000);
          }
        })
        .catch((error) => {
          console.error("Error deleting contact:", error);

          // Close the delete confirmation modal
          $("#deleteContactModal").modal("hide");
          $("#contactErrorModal").modal("show");
          setTimeout(() => {
            $("#contactErrorModal").modal("hide");
          }, 2000);
        });
    },

    resetFilters() {
      this.filters = {
        firstName: "",
        lastName: "",
        company: "",
        email: "",
        contactType: "",
      };
      this.contacts = [...this.allContacts]; // Reset to original data
      this.currentPage = 1;
      this.displayData();
    },

    // Method for pagination strip
    displayData() {
      // Update pagination based on filtered contacts
      this.itemCount = this.filteredContacts.length;
      this.pages = Math.ceil(this.itemCount / this.maxPerPage);

      // No need to fetch data again, just update the pagination
      // The paginatedContacts computed property will handle the slicing
    },
  },

  watch: {
    "filters.firstName": function () {
      this.currentPage = 1; // Reset to first page when filter changes
      this.displayData();
    },
    "filters.lastName": function () {
      this.currentPage = 1;
      this.displayData();
    },
    "filters.company": function () {
      this.currentPage = 1;
      this.displayData();
    },
    "filters.contactType": function () {
      this.currentPage = 1;
      this.displayData();
    },
    "filters.email": function () {
      this.currentPage = 1;
      this.displayData();
    },
    maxPerPage: function () {
      this.pages = Math.ceil(this.filteredContacts.length / this.maxPerPage);
    },
  },

  mounted() {
    this.fetchContacts();
    this.fetchCompanies();
  },

  template: `
      <div>
        <div class="row">
          <div class="col-md-12">
            <ul class="nav nav-tabs" role="tablist">
              <li :class="{active: activeTab === 'list'}">
                <a @click="setActiveTab('list')" data-toggle="tab" href="#contactsList">List Contacts</a>
              </li>
              <li :class="{active: activeTab === 'add'}">
                <a @click="setActiveTab('add')" data-toggle="tab" href="#addContact">Add Contact</a>
              </li>
            </ul>
    
            <div class="tab-content">
              <!-- List Contacts Tab -->
              <div id="contactsList" :class="['tab-pane', { active: activeTab === 'list' }]">
                <!-- Filters Section - Only visible when on list tab -->
                <div class="row mb-3" style="margin-top: 15px;" v-show="activeTab === 'list'">
                  <div class="col-md-12">
                    <div class="panel panel-default">
                      <div class="panel-heading">
                        <h4 class="panel-title">
                          <a data-toggle="collapse" href="#contactFilterCollapse">
                            <i class="glyphicon glyphicon-filter"></i> Filters
                          </a>
                        </h4>
                      </div>
                      <div id="contactFilterCollapse" class="panel-collapse collapse">
                        <div class="panel-body">
                          <div class="row">
                            <div class="col-md-3">
                              <div class="form-group">
                                <label>First Name</label>
                                <input type="text" class="form-control" v-model="filters.firstName" placeholder="Filter by first name">
                              </div>
                            </div>
                            <div class="col-md-3">
                              <div class="form-group">
                                <label>Last Name</label>
                                <input type="text" class="form-control" v-model="filters.lastName" placeholder="Filter by last name">
                              </div>
                            </div>
                            <div class="col-md-3">
                              <div class="form-group">
                                <label>Company</label>
                                <company-autocomplete 
                                  :companies="companies" 
                                  v-model="filters.company"
                                ></company-autocomplete>
                              </div>
                            </div>
                            <div class="col-md-3">
                              <div class="form-group">
                                <label>Contact Type</label>
                                <select class="form-control" v-model="filters.contactType">
                                  <option value="">All Types</option>
                                  <option value="PRIMARY">Primary</option>
                                  <option value="SECONDARY">Secondary</option>
                                  <option value="TECHNICAL">Technical</option>
                                  <option value="BILLING">Billing</option>
                                </select>
                              </div>
                            </div>
                          </div>
                          <div class="row">
                            <div class="col-md-3">
                              <div class="form-group">
                                <label>Email</label>
                                <input type="text" class="form-control" v-model="filters.email" placeholder="Filter by email">
                              </div>
                            </div>
                            <div class="col-md-9 text-right">
                              <button class="btn btn-default" @click="resetFilters" style="margin-top: 25px;">
                                <i class="glyphicon glyphicon-refresh"></i> Reset Filters
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
      
                <!-- Contacts Table -->
                <div class="table-responsive">
                  <table class="table table-bordered table-hover">
                    <thead>
                      <tr>
                        <pagination-strip
                          :current-page.sync="currentPage"
                          :pages="pages"
                          :max-per-page.sync="maxPerPage"
                          :item-count="itemCount"
                          @refresh="displayData"
                        ></pagination-strip>
                      </tr>
                      <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Mobile Phone</th>
                        <th>Contact Type</th>
                        <th>Company</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody v-show="!isFetching">
                      <tr v-for="contact in paginatedContacts" :key="contact.id">
                        <td>{{contact.firstName}}</td>
                        <td>{{contact.lastName}}</td>
                        <td>{{contact.email}}</td>
                        <td>{{contact.phone}}</td>
                        <td>{{contact.mobilePhone}}</td>
                        <td>{{formatContactType(contact.contactType)}}</td>
                        <td>{{getCompanyName(contact.companyId)}}</td>
                        <td>
                          <!-- Edit Button -->
                          <button class="btn btn-light" @click="editContact(contact.id)" title="Edit">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                              <path d="M12.146.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1 0 .708l-10 10a.5.5 0 0 1-.168.11l-5 2a.5.5 0 0 1-.65-.65l2-5a.5.5 0 0 1 .11-.168l10-10zM11.207 2.5 13.5 4.793 14.793 3.5 12.5 1.207 11.207 2.5zm1.586 3L10.5 3.207 4 9.707V10h.5a.5.5 0 0 1 .5.5v.5h.5a.5.5 0 0 1 .5.5v.5h.293l6.5-6.5zm-9.761 5.175-.106.106-1.528 3.821 3.821-1.528.106-.106A.5.5 0 0 1 5 12.5V12h-.5a.5.5 0 0 1-.5-.5V11h-.5a.5.5 0 0 1-.468-.325z"/>
                            </svg>
                          </button>
      
                          <!-- Delete Button -->
                          <button class="btn btn-light" @click="deleteModal(contact.id)" title="Delete">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                              <path d="M5.5 5.5A.5.5 0 0 1 6 6v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm2.5 0a.5.5 0 0 1 .5.5v6a.5.5 0 0 1-1 0V6a.5.5 0 0 1 .5-.5zm3 .5a.5.5 0 0 0-1 0v6a.5.5 0 0 0 1 0V6z"/>
                              <path fill-rule="evenodd" d="M14.5 3a1 1 0 0 1-1 1H13v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V4h-.5a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1H6a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1h3.5a1 1 0 0 1 1 1v1zM4.118 4 4 4.059V13a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                            </svg>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                    <tbody v-show="isFetching">
                      <tr>
                        <td colspan="8" class="text-center">
                          <p>Loading contacts...</p>
                        </td>
                      </tr>
                    </tbody>
                    <tfoot>
                      <tr>
                        <pagination-strip
                          :current-page.sync="currentPage"
                          :pages="pages"
                          :max-per-page.sync="maxPerPage"
                          :item-count="itemCount"
                          @refresh="displayData"
                        ></pagination-strip>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </div>
    
              <!-- Add Contact Tab -->
              <div id="addContact" :class="['tab-pane', { active: activeTab === 'add' }]">
                <div style="margin-top: 20px;">
                  <div class="row">
                    <div class="col-sm-2"></div>
                    <div class="col-sm-8">
                      <form class="form-horizontal" @submit.prevent="submitContact">
                        <div class="form-group" :class="{'has-error': validationErrors.firstName}">
                          <div class="col-sm-3">
                            <label class="control-label">First Name</label>
                          </div>
                          <div class="col-sm-6">
                            <input type="text" v-model="contactData.firstName" class="form-control" required>
                            <span class="help-block" v-if="validationErrors.firstName">{{ validationErrors.firstName }}</span>
                          </div>
                        </div>
    
                        <div class="form-group" :class="{'has-error': validationErrors.lastName}">
                          <div class="col-sm-3">
                            <label class="control-label">Last Name</label>
                          </div>
                          <div class="col-sm-6">
                            <input type="text" v-model="contactData.lastName" class="form-control" required>
                            <span class="help-block" v-if="validationErrors.lastName">{{ validationErrors.lastName }}</span>
                          </div>
                        </div>
    
                        <div class="form-group" :class="{'has-error': validationErrors.email}">
                          <div class="col-sm-3">
                            <label class="control-label">Email</label>
                          </div>
                          <div class="col-sm-6">
                            <input type="email" v-model="contactData.email" class="form-control" required>
                            <span class="help-block" v-if="validationErrors.email">{{ validationErrors.email }}</span>
                          </div>
                        </div>
    
                        <div class="form-group" :class="{'has-error': validationErrors.phone}">
                          <div class="col-sm-3">
                            <label class="control-label">Phone</label>
                          </div>
                          <div class="col-sm-6">
                            <input type="tel" v-model="contactData.phone" class="form-control">
                            <span class="help-block" v-if="validationErrors.phone">{{ validationErrors.phone }}</span>
                          </div>
                        </div>
    
                        <div class="form-group">
                          <div class="col-sm-3">
                            <label class="control-label">Mobile Phone</label>
                          </div>
                          <div class="col-sm-6">
                            <input type="tel" v-model="contactData.mobilePhone" class="form-control">
                          </div>
                        </div>
    
                        <div class="form-group" :class="{'has-error': validationErrors.contactType}">
                          <div class="col-sm-3">
                            <label class="control-label">Contact Type</label>
                          </div>
                          <div class="col-sm-6">
                            <select v-model="contactData.contactType" class="form-control" required>
                              <option value="">Select Contact Type</option>
                              <option value="PRIMARY">Primary</option>
                              <option value="SECONDARY">Secondary</option>
                              <option value="TECHNICAL">Technical</option>
                              <option value="BILLING">Billing</option>
                            </select>
                            <span class="help-block" v-if="validationErrors.contactType">{{ validationErrors.contactType }}</span>
                          </div>
                        </div>
    
                        <div class="form-group" :class="{'has-error': validationErrors.companyId}">
                          <div class="col-sm-3">
                            <label class="control-label">Company</label>
                          </div>
                          <div class="col-sm-6">
                            <company-autocomplete 
                              :companies="companies" 
                              v-model="contactData.companyId"
                            ></company-autocomplete>
                            <span class="help-block" v-if="validationErrors.companyId">{{ validationErrors.companyId }}</span>
                          </div>
                        </div>

                        <div class="form-group">
                          <div class="col-sm-3"></div>
                          <div class="col-sm-6">
                            <button type="button" @click="submitContact" class="request-button form-control">Add Contact</button>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="col-sm-2"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Edit Contact Modal -->
        <div class="modal fade" id="editContactModal" tabindex="-1" role="dialog">
          <div class="modal-dialog">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Edit Contact</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body">
                <form class="form-horizontal">
                  <div class="form-group" :class="{'has-error': editValidationErrors.firstName}">
                    <div class="col-sm-3">
                      <label class="control-label">First Name</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" v-model="editContactData.firstName" class="form-control" required>
                      <span class="help-block" v-if="editValidationErrors.firstName">{{ editValidationErrors.firstName }}</span>
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.lastName}">
                    <div class="col-sm-3">
                      <label class="control-label">Last Name</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="text" v-model="editContactData.lastName" class="form-control" required>
                      <span class="help-block" v-if="editValidationErrors.lastName">{{ editValidationErrors.lastName }}</span>
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.email}">
                    <div class="col-sm-3">
                      <label class="control-label">Email</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="email" v-model="editContactData.email" class="form-control" required>
                      <span class="help-block" v-if="editValidationErrors.email">{{ editValidationErrors.email }}</span>
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.phone}">
                    <div class="col-sm-3">
                      <label class="control-label">Phone</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="tel" v-model="editContactData.phone" class="form-control">
                      <span class="help-block" v-if="editValidationErrors.phone">{{ editValidationErrors.phone }}</span>
                    </div>
                  </div>

                  <div class="form-group">
                    <div class="col-sm-3">
                      <label class="control-label">Mobile Phone</label>
                    </div>
                    <div class="col-sm-9">
                      <input type="tel" v-model="editContactData.mobilePhone" class="form-control">
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.contactType}">
                    <div class="col-sm-3">
                      <label class="control-label">Contact Type</label>
                    </div>
                    <div class="col-sm-9">
                      <select v-model="editContactData.contactType" class="form-control" required>
                        <option value="">Select Contact Type</option>
                        <option value="PRIMARY">Primary</option>
                        <option value="SECONDARY">Secondary</option>
                        <option value="TECHNICAL">Technical</option>
                        <option value="BILLING">Billing</option>
                      </select>
                      <span class="help-block" v-if="editValidationErrors.contactType">{{ editValidationErrors.contactType }}</span>
                    </div>
                  </div>

                  <div class="form-group" :class="{'has-error': editValidationErrors.companyId}">
                    <div class="col-sm-3">
                      <label class="control-label">Company</label>
                    </div>
                    <div class="col-sm-9">
                      <company-autocomplete 
                        :companies="companies" 
                        v-model="editContactData.companyId"
                      ></company-autocomplete>
                      <span class="help-block" v-if="editValidationErrors.companyId">{{ editValidationErrors.companyId }}</span>
                    </div>
                  </div>
                </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="request-button" @click="updateContact">Save Changes</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div class="modal fade" id="deleteContactModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p style="margin-top: 10px;">Are you sure you want to delete this contact?</p>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" @click="deleteContact">Delete</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Success Modal -->
        <div class="modal fade" id="contactSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p>Contact added successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Update Success Modal -->
        <div class="modal fade" id="contactUpdateSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p style="margin-top: 10px;">Contact updated successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete Success Modal -->
        <div class="modal fade" id="contactDeleteSuccessModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Success</h5>
                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <p style="margin-top: 10px;">Contact deleted successfully!</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Modal -->
        <div class="modal fade" id="contactErrorModal" tabindex="-1" role="dialog">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Error</h5>
                <button type="button" class="close" data-dismiss="modal">
                  <span>&times;</span>
                </button>
              </div>
              <div class="modal-body text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <p style="margin-top: 10px;">An error occurred. Please try again.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
});

// Initialize the Vue application
new Vue({
  el: "#contacts-app",
});
