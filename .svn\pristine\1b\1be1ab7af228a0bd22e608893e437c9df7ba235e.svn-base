package com.accolm.licenseManager.RestInterface;

import java.util.List;

import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.accolm.licenseManager.Entities.Country;
import com.accolm.licenseManager.Services.CountryService;
import com.accolm.licenseManager.Services.CountryServiceImpl;

@Path("countries")
public class CountryRI {

	// a country service layer for separation of concerns.
	private CountryService impl = new CountryServiceImpl();


	// Endpoint to Read Countries, /api/countries.
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllCountries() {
		List<Country> result = impl.getAllCountries();
		return Response.status(Response.Status.OK).entity(result).build();
	}



}
