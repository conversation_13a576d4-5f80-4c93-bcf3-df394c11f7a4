package com.accolm.licenseManager.Services;

import java.util.List;

import com.accolm.licenseManager.Entities.MaintenanceSupport;

public interface MaintenanceSupportService {

	// speak to the MaintenanceSupportDAO method to create a maintenance-support entity.
	MaintenanceSupport createMaintenanceSupport(MaintenanceSupport request);

	// speak to the MaintenanceSupportDAO method to fetch all maintenance-support requests
	List<MaintenanceSupport> getAllMaintenanceSupportRequests();

	
	// speak to the MaintenanceSupportDAO method to fetch all maintenance-support request by id
	MaintenanceSupport getAllMaintenanceSupportRequestById(int id);

	// speak to the MaintenanceSupportDAO method to count all ms requests
	long getMaintenanceSupportRequestsCount();
	
	// Update an existing maintenance support request
		boolean updateMaintenanceSupport(MaintenanceSupport request);

	// Delete a maintenance support request by ID
	boolean deleteMaintenanceSupport(int id);


}
