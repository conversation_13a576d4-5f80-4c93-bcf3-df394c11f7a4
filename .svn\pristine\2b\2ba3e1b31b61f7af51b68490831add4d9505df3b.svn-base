package com.accolm.licenseManager.DAO;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.TypedQuery;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bouncycastle.jce.provider.BrokenJCEBlockCipher.BrokePBEWithMD5AndDES;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import com.accolm.licenseManager.Entities.Authority;
import com.accolm.licenseManager.Entities.License;
import com.accolm.licenseManager.Entities.User;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

public class AuthorityDaoImpl implements AuthorityDao {

	Logger logger = LogManager.getLogger(AuthorityDaoImpl.class);

	@Override
	public void insertUserAuthorities(User user) {

		EntityManager em = LocalEntityManagerFactory.createEntityManager();

		Authority userAuthority = null;
		// save
		try {
			logger.info("inserting user authorities...");
			// managed transactions by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.

			// create user authority
			String userRole = user.getRole().toUpperCase();
			userAuthority = switch (userRole) {

			case "ADMIN" -> new Authority(userRole);
			case "USER" -> new Authority(userRole);

			default -> null;

			};

			userAuthority.setUsername(user.getUsername());
			em.merge(userAuthority);

			tx.commit(); // commit the transaction
			logger.info("user authority saved successfully.");
		} catch (Exception e) {
			logger.error("Error saving user authority ", e);
		} finally {
			em.close(); // close persistence context.
		}

	}

	@Override
	public String updateUserAuthorities(User user) {

		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		// managed transactions by RESOURCE_LOCAL
		EntityTransaction tx = em.getTransaction(); // get a transaction to the database.
		tx.begin(); // begin a transaction.

		// save
		try {
			logger.info("update user authorities...");

			TypedQuery<Authority> query = em.createQuery(
					"SELECT userAuthority FROM Authority userAuthority WHERE userAuthority.username =:username",
					Authority.class);

			query.setParameter("username", user.getUsername());

			Authority existingUserAuthority = query.getSingleResult();
			if (existingUserAuthority != null) {

				// create user authority
				String userRole = user.getRole().toUpperCase();
				switch (userRole) {

				case "ADMIN" -> existingUserAuthority.setAuthority(userRole);
				case "USER" -> existingUserAuthority.setAuthority(userRole);


				};
				em.merge(existingUserAuthority); // save the new user Authority to the db.
				tx.commit(); // commit the transaction
				logger.info("User authority updated successfully.");
				return "Success";
			} else {
				logger.warn("User with username {} authority not found", user.getUsername());
			}

		} catch (Exception e) {
			logger.error("Error updating user authority ", e);
		} finally {
			em.close(); // close persistence context.
		}

		return "failed";

	}

	@Override
	public String deleteUserAuthorities(String userName) {

		logger.info("Removing user authority with username: {}", userName);
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();

			TypedQuery<Authority> query = em.createQuery(
					"SELECT userAuthority FROM Authority userAuthority WHERE userAuthority.username =:username",
					Authority.class);

			query.setParameter("username", userName);

			Authority theUserAuthority = query.getSingleResult();
			if (theUserAuthority != null) {
				em.remove(theUserAuthority);
				em.getTransaction().commit();
				logger.info("User authority removed successfully.");
				return "Status: User authority removed successfully";
			} else {
				logger.warn("User with userName {} not found.", userName);
			}
		} catch (Exception e) {
			logger.error("Error removing User: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return "Error: User authority not found or could not be removed.";

	}

}
