package com.accolm.licenseManager.Services;

import java.util.List;

import javax.faces.flow.builder.ReturnBuilder;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import com.accolm.licenseManager.DAO.CompanyDAO;
import com.accolm.licenseManager.DAO.CompanyDAOImpl;
import com.accolm.licenseManager.DAO.MaintenanceSupportDAO;
import com.accolm.licenseManager.DAO.MaintenanceSupportDAOImpl;
import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Entities.MaintenanceSupport;

public class MaintenanceSupportServiceImpl implements MaintenanceSupportService {

	MaintenanceSupportDAO impl;

	Logger logger = LogManager.getLogger(LicenseServiceImpl.class);

	public MaintenanceSupportServiceImpl() {
		impl = new MaintenanceSupportDAOImpl();
	}

	@Override
	public MaintenanceSupport createMaintenanceSupport(MaintenanceSupport request) {

		return impl.createMaintenanceSupport(request);
	}

	@Override
	public List<MaintenanceSupport> getAllMaintenanceSupportRequests() {
		// TODO Auto-generated method stub
		return impl.fetchAllMaintenanceSupportRequests();
	}

	@Override
	public MaintenanceSupport getAllMaintenanceSupportRequestById(int id) {
		// TODO Auto-generated method stub
		return impl.fetchAllMaintenanceSupportRequestById(id);
	}

	@Override
	public long getMaintenanceSupportRequestsCount() {
		// TODO Auto-generated method stub
		return impl.countMaintenanceSupportRequests();

	}
	
	@Override
	public boolean updateMaintenanceSupport(MaintenanceSupport request) {
		logger.info("Calling DAO to update MaintenanceSupport with ID: {}", request.getId());
		return impl.updateMaintenanceSupport(request);
	}

	@Override
	public boolean deleteMaintenanceSupport(int id) {
		logger.info("Calling DAO to delete MaintenanceSupport with ID: {}", id);
		return impl.deleteMaintenanceSupport(id);
	}


}
