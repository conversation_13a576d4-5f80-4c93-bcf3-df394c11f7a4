package com.accolm.licenseManager.Security.Filters;

import java.util.List;

import javax.servlet.annotation.WebFilter;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.core.io.Resource;
import org.springframework.security.web.FilterChainProxy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.filter.DelegatingFilterProxy;

import com.accolm.licenseManager.Security.LicenseManagerApplicationSecurityConfiguration;

/**
 * Servlet Filter implementation class BaseFilter is a DelegatingFilterProxy
 */
@WebFilter("/*") // invoke on /* requests.
public class BaseFilter extends DelegatingFilterProxy {
	
	final static Logger logger = LogManager.getLogger(BaseFilter.class);


	static FilterChainProxy filterChainProxy; // host a delegation of filters

	static AnnotationConfigApplicationContext springBeanContext; // wrap inside the servlet container for spring beans.

	public BaseFilter() {
		super(configFilterChainProxy()); // initialise the mother object.
	}

	// create the filterChainProxy
	private static FilterChainProxy configFilterChainProxy() {
		try {
			springBeanContext = new AnnotationConfigApplicationContext(LicenseManagerApplicationSecurityConfiguration.class); // get spring beans
																									// from the spring


			SecurityFilterChain appFilterChainsChain = (SecurityFilterChain) springBeanContext
					.getBean("appSecurityFilterChain");

           // security exception fix for exception translation filter by spring.
			overrideSpringSecurityDefaultFiltersWithLicenseManagerOnes(appFilterChainsChain); // object in memory
			filterChainProxy = new FilterChainProxy(appFilterChainsChain); // insert the spring filter beans in the chain container

		} catch (Exception e) {
			e.printStackTrace();
		}
		return filterChainProxy;
	}

	private static void overrideSpringSecurityDefaultFiltersWithLicenseManagerOnes(SecurityFilterChain appFilterChainsChain) {
		logger.info("listing spring security default filters");
		appFilterChainsChain.getFilters().forEach(logger::info);
		appFilterChainsChain.getFilters().removeIf(filter-> filter.toString().startsWith("org.springframework.security.web.access.ExceptionTranslationFilter"));
		appFilterChainsChain.getFilters().removeIf(filter-> filter.toString().startsWith("org.springframework.security.web.authentication.logout.LogoutFilter"));
		logger.info("current list of spring security filters");
		appFilterChainsChain.getFilters().forEach(logger::info);
	}
	
}
