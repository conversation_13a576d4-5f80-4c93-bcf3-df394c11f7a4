package com.accolm.licenseManager.Services;

import java.util.List;

import com.accolm.licenseManager.Entities.Company;

public interface CompanyService {

	// speak to the CompanyDAO method to create companies.
	Company createCompany(Company compnay);
	
	// speak to the CompanyDAO method to list companies
	List<Company> getAllCompanies();

	// speak to the CompanyDAO method to get count of companies
	long getCountOfCompanies();

	
	// speak to the CompanyDAO method to get a company
	Company getCompanyById(int id);


	// speak to the CompanyDAO method to delete company.
	boolean deleteCompany(int id);

	// speak to the CompanyDAO method to update company.
	Company updateCompany(Company company);

	// do a search for a company.
	List<Company> searchCompany(String companyName);


}
