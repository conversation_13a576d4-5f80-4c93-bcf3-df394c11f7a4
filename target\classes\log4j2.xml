<?xml version="1.0" encoding="UTF-8"?>
<Configuration>
	<Appenders>
		<RollingFile name="mainLogger"
			fileName="${sys:catalina.base}/logs/licensingManager/licensingManager.log"
			filePattern="${sys:catalina.base}/logs/licensingManager-%d{MM-dd-yyyy}.%i.log">
			<PatternLayout>
				<Pattern>%d %p [%t] - %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
			<DefaultRolloverStrategy max="10" />
		</RollingFile>

		<RollingFile name="errorLogger"
			fileName="${sys:catalina.base}/logs/licensingManager_err.log"
			filePattern="${sys:catalina.base}/logs/licensingManager_err-%d{MM-dd-yyyy}.%i.log">
			<PatternLayout>
				<Pattern>%d %p [%t] - %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy interval="1" />
				<SizeBasedTriggeringPolicy size="100 MB" />
			</Policies>
			<DefaultRolloverStrategy max="10" />
		</RollingFile>

		<Console name="STDOUT" target="SYSTEM_OUT">
            <PatternLayout
                pattern="%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n" />
        </Console>
	</Appenders>

	<Loggers>
		<logger name="dev" level="info" />
		<logger name="org.springframework.security" level="info" />
		<logger name="org.eclipse.persistence" level="finest" />

		<logger name="ErrorLogger" level="ERROR" additivity="false">
			<appender-ref ref="errorLogger" />
		</logger>

		<Root level="DEBUG">
			<AppenderRef ref="mainLogger" />
		</Root>
	</Loggers>
</Configuration>