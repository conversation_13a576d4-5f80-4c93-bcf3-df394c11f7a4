var headBar = Vue.component("headbar", {
  template: `
    <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="index.html">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-lock"></i>
                </div>
                <div class="sidebar-brand-text mx-3">License Manager <sup></sup></div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="index.html">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>
            </li>

            <!-- Nav Item - Pages Collapse Menu -->
            <li class="nav-item ">
                <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseTwo"
                aria-expanded="true" aria-controls="collapseTwo">
                    <i class="fas fa-fw fa-folder-open"></i>
                    <span>License</span>
                </a>
                <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <a class="collapse-item " href="licenses.html">List License</a>
                        <a class="collapse-item " href="blanklicense.html">New Blank License</a>
                        <a class="collapse-item" href="templatelicense.html">License From Template</a>
                         <a class="collapse-item" href="importlicense.html"
                >Import License</a
              >
                    </div>
                </div>
            </li>
            
           <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseThree"
          >
            <i class="fas fa-fw fa-lock"></i>
            <span>Key Manager</span>
          </a>
          <div
            id="collapseThree"
            class="collapse"
            aria-labelledby="headingThree"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
            <a class="collapse-item" href="listkeys.html"
                >List Signing Keys</a
              >
              <a class="collapse-item " href="keymanager.html"
                >New Signing Key</a
              >
              
              <a class="collapse-item" href="importkey.html">Import Key</a>
            </div>
          </div>
        </li>
            
        <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseOne"
          >
            <i class="fas fa-fw fa-folder"></i>
            <span>Template Manager</span>
          </a>
          <div
            id="collapseOne"
            class="collapse"
            aria-labelledby="headingTwo"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
              <a class="collapse-item" href="templates.html">List Template</a>
              <a class="collapse-item" href="templatemanager.html"
                >New Template</a
              >
              <a class="collapse-item" href="importtemplate.html"
                >Import Template</a
              >
            </div>
          </div>
        </li>
        
         <!-- companies section -->
        <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseFour"
          >
            <i class="fas fa-fw fa-building"></i>
            <span>Companies</span>
          </a>
          <div
            id="collapseFour"
            class="collapse"
            aria-labelledby="headingFour"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
              <a class="collapse-item" href="companies.html">List Companies</a>
              <a class="collapse-item" href="company-new.html">New Company</a>
            </div>
          </div>
        </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>
        </ul>
	`,
});

var toolBar = Vue.component("toolbar", {
  template: `
    <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">License From Template</h1>
                    </div>

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">

                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">User</span>
                                <img class="img-profile rounded-circle"
                                    src="Assets/img/undraw_profile.svg">
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Profile
                                </a>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Activity Log
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
	`,
});

var scrollToTop = Vue.component("scrolltotop", {
  template: `
    <a class="scroll-to-top rounded" href="#page-top">
            <i class="fas fa-angle-up"></i>
        </a>
	`,
});

var footBar = Vue.component("footbar", {
  template: `
    <footer class="sticky-footer bg-white">
        <div class="container my-auto">
            <div class="copyright text-center my-auto">
                <span>Copyright &copy; Accolm License Manager 2021</span>
            </div>
        </div>
    </footer>
	`,
});

// Add to components section
Vue.component("success-modal", {
  template: `
      <div class="modal fade" id="successModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onSuccessModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-body text-center">
              <i class="fa fa-check bg-success align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
              <p class="lead text-success mb-5">
                <strong>Successfully Created License!</strong>
              </p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    `,
});

Vue.component("error-modal", {
  template: `
      <div class="modal fade" id="errorModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onFailureModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-body text-center">
              <i class="fa fa-times bg-danger align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
              <p class="lead text-danger mb-5">
                <strong>Failed to Create License...</strong>
              </p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    `,
});

var app = new Vue({
  el: "#app",
  data: {
    showSuccessModal: false,
    showErrorModal: false,
    rowCount: 1,
    templates: [],
    newLicense: {
      name: "",
      status: "Valid",
      creationDate: new Date().toISOString().slice(0, 10), // Sets current date as MM/DD/YYYY
      startDate: "",
      expiryDate: "",
      floatExp: "",
      hardwareLock: "",
      signKey: {
        id: 0,
        type: "",
        name: "",
        public_key: "",
        private_key: "",
      },
      properties: [{}],
    },
    template: {
      id: 0,
      name: "",
      licenseExpiry: "",
      floatExpiry: 0,
      templateProperties: [],
    },
    signingKey: {
      id: 0,
      type: "",
      name: "",
      public_key: "",
      private_key: "",
    },
    signingKeys: [],
  },
  components: {
    headbar: headBar,
    scrolltotop: scrollToTop,
    toolbar: toolBar,
    footbar: footBar,
    "success-modal": Vue.component("success-modal"),
    "error-modal": Vue.component("error-modal"),
  },
  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1]; // Gets the first segment after domain
      return `${baseUrl}/${projectName}/api`;
    },
  },
  methods: {
    async onSubmitLicense() {
      try {
        if (this.newLicense.signKey.id == 0) {
          this.newLicense.signKey = {};
        }

        const res = await axios.post(
          `${this.apiBaseUrl}/licenses/create`,
          this.newLicense
        );

        // Check for both 200 and 201 success status codes
        if (res.status === 200 || res.status === 201) {
          // Show success modal
          $("#successModal").modal("show");

          // Reload page after modal is closed
          $("#successModal").on("hidden.bs.modal", function () {
            window.location.reload();
          });
        }
      } catch (error) {
        // Only show error modal if API call actually fails
        $("#errorModal").modal("show");
        console.log("error", error);
      }
    },
    async getTemplates() {
      const res = await axios.get(`${this.apiBaseUrl}/templates/all`);

      if (res.status == 200) {
        this.templates = res.data;
      } else {
        console.log("error", res);
      }
    },
    async getSigningKeys() {
      const res = await axios.get(`${this.apiBaseUrl}/keys/all`);

      if (res.status == 200) {
        this.signingKeys = res.data;
      } else {
        console.log("error", res);
      }
    },
    async getTemplate(id) {
      const res = await axios.get(`${this.apiBaseUrl}/templates/get/${id}`);

      if (res.status == 200) {
        this.template = res.data;

        this.newLicense.floatExp = this.template.floatExpiry;
        this.newLicense.properties = this.template.templateProperties;
      } else {
        console.log("error", res);
      }
    },
    async getSigningKey(id) {
      this.signingKeys.forEach((key) => {
        if (key.id == id) {
          this.signingKey = key;
          console.log(this.signingKey);
          this.newLicense.signKey = this.signingKey;
        }
      });

      // const res = await axios.get('http://localhost:8080/LicenseManager/api/keys/get/' + id);

      // if (res.status == 200) {

      //     this.signingKey = res.data;

      // } else {
      //     console.log('error', res);
      // }
    },
    editProp(id) {
      console.log("Item with id: " + id + " is edited..");
    },
    deleteProp(id) {
      console.log("Item with id: " + id + " is deleted..");
    },
    onTemplateChange(e) {
      const id = e.target.value;
      if (id > 0) {
        this.getTemplate(id);
      }
    },
    onSigningKeyChange(e) {
      const id = e.target.value;
      if (id > 0) {
        this.getSigningKey(id);
      }
    },
    removeProp(row) {
      if (this.rowCount == 1) {
        console.log(this.rowCount);
        this.disableBtn = true;
        // this.rowCount++;
      } else if (this.rowCount > 1) {
        console.log(this.rowCount);
        this.newLicense.properties.splice(row, 1);
        this.rowCount--;
      }
    },
    addProp() {
      if (this.rowCount >= 1) {
        // this.newLicense.properties.push(property);
        console.log(this.newLicense.properties);
        this.newLicense.properties.push({
          name: "",
          value: "",
        });
        this.disableBtn = false;
        // this.propName = "";
        // this.propValue = "";
        console.log(this.rowCount);

        this.rowCount++;
      } else if (this.rowCount == 0) {
        this.newLicense.properties.push({
          name: "",
          value: "",
        });
        this.rowCount++;
      }
    },
  },
  beforeMount: function () {
    this.getTemplates();
    this.getSigningKeys();
  },
});
