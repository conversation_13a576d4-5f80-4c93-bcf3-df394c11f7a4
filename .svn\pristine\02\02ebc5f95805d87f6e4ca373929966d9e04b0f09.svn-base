const EventBus = new Vue();

var manager = Vue.component("new-key", {
  data() {
    return {
      keys: [],
      key: {},
      hasSaved: false,
      errors: [],
    };
  },

  methods: {
    validateKeyForm() {
      this.errors = [];
      if (!this.key.name || this.key.name.trim() === "") {
        this.errors.push("Please enter a key name");
        $("#validationModal").modal("show");
        return false;
      }
      return true;
    },

    generateKey() {
      if (!this.validateKeyForm()) {
        return;
      }

      this.isLoading = true;
      const name = this.key.name ? `?name=${this.key.name}` : "/default";

      axios
        .post(`${this.apiBaseUrl}/keys/generate${name}`)
        .then((response) => {
          this.key = response.data;
          EventBus.$emit(
            "show-message-box",
            "Key generated successfully",
            true
          );
        })
        .catch((error) => {
          console.log("Error details:", {
            status: error.response.status,
            data: error.response.data,
            headers: error.response.headers,
          });
          EventBus.$emit(
            "show-message-box",
            "Failed to generate key. Please try again.",
            false
          );
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    async saveKey() {
      if (!this.validateKeyForm()) {
        return;
      }

      this.isLoading = true;

      await axios
        .post(
          `${this.apiBaseUrl}/keys/save`,
          JSON.parse(JSON.stringify(this.key)),
          {
            headers: {
              "Content-Type": "application/json",
            },
          }
        )
        .then((response) => {
          this.hasSaved = true;
          // Show success modal
          $("#onSaveModal").modal("show");
          // Emit event for list refresh
          EventBus.$emit("key-saved");
        })
        .catch((error) => {
          // Show error modal
          $("#onFailureModal").modal("show");
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    async validateKey(key) {
      this.isLoading = true;

      await axios
        .post(`${this.apiBaseUrl}/keys/save`, JSON.parse(JSON.stringify(key)), {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((response) => {
          $("#validateKeyNotification").hide();
          var message = response.message;
          var isSuccessful = response.status;
          EventBus.$emit("show-message-box", message, isSuccessful);
          if (isSuccessful) {
            this.saveUpdateKey(key);
          }
        })
        .catch((error) => {
          var message = "Could not validate key. An error occurred";
          var isSuccessful = false;
          EventBus.$emit("show-message-box", message, isSuccessful);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },

    reloadPage() {
      window.location.reload(true);
    },
  },

  computed: {
    apiBaseUrl() {
      const baseUrl = window.location.origin;
      const pathSegments = window.location.pathname.split("/");
      const projectName = pathSegments[1];
      return `${baseUrl}/${projectName}/api`;
    },
  },

  template: `
    <form class="form-horizontal" @submit.prevent="saveKey">
        <div class="form-group row">
            <label class="col-sm-2 text-left control-label col-form-label">Key Name</label>
            <div class="col-sm-4">
                <input autocomplete="off" v-model="key.name" type="text" name="name" class="form-control" placeholder="">
            </div>
            <label class="col-sm-2 text-left control-label col-form-label">Key Type</label>
            <div class="col-sm-4">
                <input autocomplete="off" v-model="key.type" type="text" name="type" class="form-control" disabled placeholder="">
            </div>
        </div>
        <div class="form-group row">
            <div class="col-sm-2"></div>
            <div class="col-sm-4">
                <button v-on:click.prevent='generateKey()'>Generate Key</button>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-sm-2 text-left control-label col-form-label">Key Value</label>
            <div class="col-sm-4">
                <textarea disabled v-model="key.public_key" name="value" id="inputValue" cols="50" rows="5"></textarea>
            </div>
        </div>
        <div class="border-top">
            <div class="card-body">
                <button type="submit" data-target="#SaveModal" data-toggle="modal" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Signing Key
                </button>
            </div>
        </div>

        <!-- Modals -->
        <div class="modal fade" id="onSaveModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onSaveModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fa fa-check bg-success align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                        <p class="lead text-success mb-5">
                            <strong>Key Saved Successfully...</strong>
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" @click="reloadPage()">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="validationModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="validationModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fa fa-exclamation-triangle bg-warning align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                        <p class="lead text-warning mb-5">
                            <strong>Please enter a key name before generating</strong>
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="onGenerateFailureModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onFailureModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fa fa-times bg-danger align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                        <p class="lead text-danger mb-5">
                            <strong>Failed to Generate Key...</strong>
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="onFailureModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onFailureModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fa fa-times bg-danger align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                        <p class="lead text-danger mb-5">
                            <strong>Failed to Save Key...</strong>
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="onDuplicateModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="onDuplicateModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-body text-center">
                        <i class="fa fa-check bg-danger align-middle text-light p-3 mt-4 mb-2" style="font-size: 50px; border-radius: 60px;"></i>
                        <p class="lead text-danger mb-5">
                            <strong>There is another key with this name, try saving it with another name</strong>
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
  `,
});

var app = new Vue({
  el: "#keycomp",
  components: {
    "new-key": manager,
  },
  template: `
    <div>
      <!-- Page Wrapper -->
      <div id="wrapper">
          <!-- Sidebar -->
          <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">
              <!-- Sidebar - Brand -->
              <a class="sidebar-brand d-flex align-items-center justify-content-center" href="index.html">
                  <div class="sidebar-brand-icon rotate-n-15">
                      <i class="fas fa-lock"></i>
                  </div>
                  <div class="sidebar-brand-text mx-3">License Manager <sup></sup></div>
              </a>

              <!-- Divider -->
              <hr class="sidebar-divider my-0">

              <!-- Nav Item - Dashboard -->
              <li class="nav-item">
                  <a class="nav-link" href="index.html">
                      <i class="fas fa-fw fa-tachometer-alt"></i>
                      <span>Dashboard</span></a>
              </li>

              <!-- Nav Item - License Menu -->
              <li class="nav-item">
                  <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseTwo">
                      <i class="fas fa-fw fa-folder-open"></i>
                      <span>License</span>
                  </a>
                  <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                      <div class="bg-white py-2 collapse-inner rounded">
                          <a class="collapse-item" href="licenses.html">List License</a>
                          <a class="collapse-item" href="blanklicense.html">New Blank License</a>
                          <a class="collapse-item" href="templatelicense.html">License From Template</a>
                          <a class="collapse-item" href="importlicense.html">Import License</a>
                      </div>
                  </div>
              </li>

              <!-- Nav Item - Key Manager Menu -->
              <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseThree"
          >
            <i class="fas fa-fw fa-lock"></i>
            <span>Key Manager</span>
          </a>
          <div
            id="collapseThree"
            class="collapse"
            aria-labelledby="headingThree"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
            <a class="collapse-item" href="listkeys.html"
                >List Signing Keys</a
              >
              <a class="collapse-item " href="keymanager.html"
                >New Signing Key</a
              >
              
              <a class="collapse-item" href="importkey.html">Import Key</a>
            </div>
          </div>
        </li>

              <!-- Nav Item - Template Manager Menu -->
              <li class="nav-item">
                  <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapseOne">
                      <i class="fas fa-fw fa-folder"></i>
                      <span>Template Manager</span>
                  </a>
                  <div id="collapseOne" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                      <div class="bg-white py-2 collapse-inner rounded">
                          <a class="collapse-item" href="templates.html">List Template</a>
                          <a class="collapse-item" href="templatemanager.html">New Template</a>
                          <a class="collapse-item" href="importtemplate.html">Import Template</a>
                      </div>
                  </div>
              </li>
              
               <!-- companies section -->
        <li class="nav-item">
          <a
            class="nav-link collapsed"
            href="#"
            data-toggle="collapse"
            data-target="#collapseFour"
          >
            <i class="fas fa-fw fa-building"></i>
            <span>Companies</span>
          </a>
          <div
            id="collapseFour"
            class="collapse"
            aria-labelledby="headingFour"
            data-parent="#accordionSidebar"
          >
            <div class="bg-white py-2 collapse-inner rounded">
              <a class="collapse-item" href="companies.html">List Companies</a>
              <a class="collapse-item" href="company-new.html">New Company</a>
            </div>
          </div>
        </li>

              <!-- Divider -->
              <hr class="sidebar-divider d-none d-md-block">

              <!-- Sidebar Toggler -->
              <div class="text-center d-none d-md-inline">
                  <button class="rounded-circle border-0" id="sidebarToggle"></button>
              </div>
          </ul>

          <!-- Content Wrapper -->
          <div id="content-wrapper" class="d-flex flex-column">
              <!-- Main Content -->
              <div id="content">
                  <!-- Topbar -->
                  <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                      <div class="d-sm-flex align-items-center justify-content-between mb-4">
                          <h1 class="h3 mb-0 text-gray-800">New Signing Key</h1>
                      </div>
                      <ul class="navbar-nav ml-auto">
                          <div class="topbar-divider d-none d-sm-block"></div>
                          <!-- Nav Item - User Information -->
                          <li class="nav-item dropdown no-arrow">
                              <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                  data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                  <span class="mr-2 d-none d-lg-inline text-gray-600 small">User</span>
                                  <img class="img-profile rounded-circle" src="Assets/img/undraw_profile.svg">
                              </a>
                              <!-- Dropdown - User Information -->
                              <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                  aria-labelledby="userDropdown">
                                  <a class="dropdown-item" href="#">
                                      <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                      Profile
                                  </a>
                                  <a class="dropdown-item" href="#">
                                      <i class="fas fa-list fa-sm fa-fw mr-2 text-gray-400"></i>
                                      Activity Log
                                  </a>
                                  <div class="dropdown-divider"></div>
                                  <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                      <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                      Logout
                                  </a>
                              </div>
                          </li>
                      </ul>
                  </nav>

                  <!-- Begin Page Content -->
                  <div class="container-fluid">
                      <div class="row">
                          <div class="col-lg-12">
                              <div class="card shadow mb-4">
                                  <div class="card-header py-3">
                                      <h6 class="m-0 font-weight-bold text-primary">New Signing Key</h6>
                                  </div>
                                  <div class="card-body">
                                      <new-key></new-key>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>

              <!-- Footer -->
              <footer class="sticky-footer bg-white">
                  <div class="container my-auto">
                      <div class="copyright text-center my-auto">
                          <span>Copyright &copy; Accolm License Manager 2021</span>
                      </div>
                  </div>
              </footer>
          </div>
      </div>

      <!-- Scroll to Top Button-->
      <a class="scroll-to-top rounded" href="#page-top">
          <i class="fas fa-angle-up"></i>
      </a>

      <!-- Logout Modal-->
      <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
          aria-hidden="true">
          <div class="modal-dialog" role="document">
              <div class="modal-content">
                  <div class="modal-header">
                      <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                      <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                          <span aria-hidden="true">×</span>
                      </button>
                  </div>
                  <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                  <div class="modal-footer">
                      <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                      <a class="btn btn-primary" href="#">Logout</a>
                  </div>
              </div>
          </div>
      </div>
    </div>
`,
});
