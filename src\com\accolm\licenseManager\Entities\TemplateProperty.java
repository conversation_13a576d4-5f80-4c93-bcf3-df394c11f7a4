package com.accolm.licenseManager.Entities;

import javax.persistence.*;

@Entity
@Table(name = "templateproperty")
public class TemplateProperty {

    @Id
    @Column(name = "TemplateProperty_ID")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "TemplatePropertyName", nullable = false)
    private String name;

    @Column(name = "TemplatePropertyValue", nullable = true)
    private String value;

    // Default Constructor
    public TemplateProperty() {}

    // Parameterized Constructor
    public TemplateProperty(int id, String name, String value) {
        this.id = id;
        this.name = name;
        this.value = value;
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "TemplateProperty{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", value='" + value + '\'' +
                '}';
    }
}
