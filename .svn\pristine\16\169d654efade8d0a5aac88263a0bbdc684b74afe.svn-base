package com.accolm.licenseManager.Services;

import java.util.List;

import javax.faces.flow.builder.ReturnBuilder;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.transaction.annotation.Transactional;

import com.accolm.licenseManager.DAO.CompanyDAO;
import com.accolm.licenseManager.DAO.CompanyDAOImpl;
import com.accolm.licenseManager.Entities.Company;

public  class CompanyServiceImpl implements CompanyService {

	CompanyDAO impl;

	Logger logger = LogManager.getLogger(LicenseServiceImpl.class);

	public CompanyServiceImpl() {
		impl = new CompanyDAOImpl();
	}

	@Override
	public Company createCompany(Company compnay) {

		Company theCompany = impl.createCompany(compnay);

		return theCompany;
	}

	@Override
	public List<Company> getAllCompanies() {

		List<Company> companies = impl.getAllCompanies();

		return companies;
	}

	@Override
	public long getCountOfCompanies() {

		return impl.getCountOfCompanies();
	}

	@Override
	public Company getCompanyById(int id) {
		// TODO Auto-generated method stub
		return impl.getCompanyById(id);
	}

	
	
	@Override
	public boolean deleteCompany(int id) {
		
		return impl.deleteCompany(id);
	}

	@Override
	public Company updateCompany(Company company) {

		
		return impl.updateCompany(company);
	}
 
	
	@Override
	public List<Company> searchCompany(String companyName) {

		List<Company> companies = impl.searchCompany(companyName);

		return companies;

	}
	
	


}
