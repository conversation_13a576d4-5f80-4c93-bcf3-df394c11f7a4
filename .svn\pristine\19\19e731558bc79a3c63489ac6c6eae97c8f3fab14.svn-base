package com.accolm.licenseManager.DAO;

import java.io.*;
import java.security.KeyPair;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Date;

import org.apache.commons.codec.binary.Hex;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.Entities.SigningKey;
import com.accolm.licenseManager.Root.KeyManager;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

import javax.persistence.EntityManager;
import javax.persistence.Query;

public class SigningKeyDao {

	// Private Fields
	ArrayList<SigningKey> keys = null;
	SigningKey instance;
	String name, type, value, message = "";

	// Logger Instance
	Logger logger = LogManager.getLogger(SigningKeyDao.class);


	// Retrieve signing keys with pagination logic
	public List<SigningKey> getSigningKeys(int page) {
		logger.info("================================================================================");
		logger.info("Starting to get signing keys for page: {}", page);

		long start = System.currentTimeMillis();

		logger.info("Acquiring persistence context...");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		logger.info("Persistence context acquired.");

		int pageSize = 10; // Display 10 keys per page

		try {
			logger.info("Beginning transaction...");
			em.getTransaction().begin();
			logger.info("Transaction begun.");

			logger.info("Executing query for paginated results...");

			// Fetch signing keys with pagination (ordered by ID for consistency)
			List<SigningKey> keys = em.createQuery("SELECT k FROM SigningKey k", SigningKey.class)
//                                      .setFirstResult(page * pageSize)  // Skip (page * 10) records
//                                      .setMaxResults(pageSize)         // Limit to 10 records per page
					.getResultList();

			logger.info("Query executed successfully.");

			logger.info("Committing transaction...");
			em.getTransaction().commit();
			logger.info("Transaction committed.");

			return keys;

		} catch (IllegalArgumentException e) {
			logger.error("Error executing query. Details: ", e);
		} catch (Exception e) {
			logger.error("Unexpected error occurred: ", e);
			em.getTransaction().rollback();
		} finally {
			logger.info("Closing persistence context...");
			em.close();
			logger.info("Retrieving data took " + (System.currentTimeMillis() - start) + "ms.");
			logger.info("================================================================================\n");
		}

		return new ArrayList<>(); // Return an empty list if there's an error
	}

	// Retrieve signing key by ID
	public SigningKey get(int id) {
		logger.info("================================================================================");
		logger.info("Starting get signing key by ID Dao..");

		long start = System.currentTimeMillis();

		logger.info("Acquiring persistence context..");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		logger.info("Persistence context acquired..");

		try {
			logger.info("Beginning transaction..");
			em.getTransaction().begin();
			logger.info("Transaction begun..");

			logger.info("Searching for key with id: " + id + "..");
			SigningKey key = em.find(SigningKey.class, id);

			em.getTransaction().commit();
			logger.info("Transaction committed..");

			if (key != null) {
				logger.info("Key found..");
				return key;
			} else {
				logger.info("No key found for id: " + id);
			}
		} catch (Exception e) {
			logger.error("Error retrieving signing key with ID " + id + ": ", e);
			em.getTransaction().rollback();
		} finally {
			logger.info("Closing persistence context..");
			em.close();
			logger.info("Retrieving data took " + (System.currentTimeMillis() - start) + "ms.");
			logger.info("================================================================================\n");
		}

		return null;
	}

	// Add a new signing key
	public SigningKey add(SigningKey Key) {
		logger.info("================================================================================");
		logger.info("Starting add signing key Dao..");

		long start = System.currentTimeMillis();

		// Validate key pair
		if (Key.getPublic_key() == null || Key.getPrivate_key() == null ||
		    Key.getPublic_key().isEmpty() || Key.getPrivate_key().isEmpty()) {
			logger.warn("Attempted to add a signing key without public/private key data.");
			return null; // Or you can throw an exception if preferred
		}
		
		logger.info("Acquiring persistence context..");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		logger.info("Persistence context acquired..");

		try {
			
			logger.info("Beginning transaction..");
			em.getTransaction().begin();
			logger.info("Transaction begun..");
			
			// Duplicate name check
	        Query nameQuery = em.createQuery("SELECT COUNT(k) FROM SigningKey k WHERE k.name = :name");
	        nameQuery.setParameter("name", Key.getName());
	        long nameCount = (long) nameQuery.getSingleResult();
	        if (nameCount > 0) {
	            logger.warn("A key with the name '{}' already exists.", Key.getName());
	            return null;
	        }

	        // Duplicate public/private key pair check
	        Query keyPairQuery = em.createQuery(
	            "SELECT COUNT(k) FROM SigningKey k WHERE k.public_key = :pub AND k.private_key = :priv");
	        keyPairQuery.setParameter("pub", Key.getPublic_key());
	        keyPairQuery.setParameter("priv", Key.getPrivate_key());
	        long pairCount = (long) keyPairQuery.getSingleResult();
	        if (pairCount > 0) {
	            logger.warn("A key with the same public/private key pair already exists.");
	            return null;
	        }


			logger.info("Attempting to add Key..");
			Key = em.merge(Key);
			logger.info("Key added successfully..");

			logger.info("Committing transaction..");
			em.getTransaction().commit();
			logger.info("Transaction committed..");

			return Key;
		} catch (IllegalArgumentException e) {
			logger.error("The id (" + Key.getId() + ") does not exist. \nError In Details: \n" + e);
		} catch (Exception e) {
			logger.error(e);
			em.getTransaction().rollback();
		} finally {
			logger.info("Closing persistence context..");
			em.close();
			logger.info("Adding data took " + (System.currentTimeMillis() - start) + "ms.");
			logger.info("================================================================================\n");
		}

		return null;
	}

	// Update an existing signing key
	public String update(SigningKey Key) {
		logger.info("================================================================================");
		logger.info("Starting update key..");

		long start = System.currentTimeMillis();

		logger.info("Acquiring persistence context..");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		logger.info("Persistence context acquired..");
		try {
			logger.info("Beginning transaction..");
			em.getTransaction().begin();
			logger.info("Transaction begun..");

			logger.info("Searching for key with id: " + Key.getId() + "..");
			SigningKey k = em.find(SigningKey.class, Key.getId());
			if (k == null) {
				logger.error("No signing key found with ID " + Key.getId());
				return "Key not found";
			}
			logger.info("Key found..");

			logger.info("Updating Key..");
			k.setName(Key.getName());
			k.setType(Key.getType());
			k.setValue(Key.getValue());
			k.setPublic_key(Key.getPublic_key());
			k.setPrivate_key(Key.getPrivate_key());
			logger.info("Key Updated..");

			logger.info("Committing transaction..");
			em.getTransaction().commit();
			logger.info("Transaction committed..");

			return "Update successful";
		} catch (IllegalArgumentException e) {
			logger.error("The id (" + Key.getId() + ") does not exist. \nError In Details: \n" + e);
		} catch (NullPointerException e) {
			logger.error("A null object has been passed. \nError In Details: \n" + e);
		} catch (Exception e) {
			logger.error(e);
		} finally {
			logger.info("Closing persistence context..");
			em.close();
			logger.info("Updating data took " + (System.currentTimeMillis() - start) + "ms.");
			logger.info("================================================================================\n");
		}

		return "Update failed";
	}

	// Delete signing key by ID
	public String delete(int id) {
		logger.info("================================================================================");
		logger.info("Starting delete key API..");

		long start = System.currentTimeMillis();

		logger.info("Acquiring persistence context..");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		logger.info("Persistence context acquired..");

		try {

			logger.info("Beginning transaction..");
			em.getTransaction().begin();
			logger.info("Transaction begun..");

			logger.info("Searching for key with id: " + id + "..");
			SigningKey k = em.find(SigningKey.class, id);
			if (k == null) {
				logger.error("No signing key found with ID " + id);
				return "Key not found";
			}
			logger.info("Key found..");

			logger.info("Deleting key..");
			em.remove(k);
			logger.info("Key deleted successfully..");

			logger.info("Committing transaction..");
			em.getTransaction().commit();
			logger.info("Transaction committed..");

			return "Delete successful";
		} catch (IllegalArgumentException e) {
			logger.error("The id (" + id + ") does not exist. \nError In Details: \n" + e);
		} catch (NullPointerException e) {
			logger.error("A null object has been passed. \nError In Details: \n" + e);
		} catch (Exception e) {
			logger.error(e);
		} finally {
			logger.info("Closing persistence context..");
			em.close();
			logger.info("Deleting data took " + (System.currentTimeMillis() - start) + "ms.");
			logger.info("================================================================================\n");
		}

		return "Delete failed";
	}

	// Count signing keys
	public long count() {
		logger.info("================================================================================");
		logger.info("Starting count signing keys..");

		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			Query query = em.createQuery("SELECT COUNT(k) FROM SigningKey k");
			return (long) query.getSingleResult();
		} catch (Exception e) {
			logger.error("Error counting signing keys: ", e);
		} finally {
			em.close();
		}
		return 0;
	}

	public KeyPair getKey() {
		logger.info("Generating a new cryptographic KeyPair...");
		return KeyManager.createKeyPair();
	}

	public SigningKey generateSigningKey(String keyName) {
		logger.info("================================================================================");
		logger.info("Starting generate signing key Dao...");

		long start = System.currentTimeMillis();
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		
		// Check if a key with the same name already exists
		Query query = em.createQuery("SELECT COUNT(k) FROM SigningKey k WHERE k.name = :name");
		query.setParameter("name", keyName);
		long count = (long) query.getSingleResult();
		if (count > 0) {
		    logger.warn("A key with the name '{}' already exists.", keyName);
		    return null; // Or throw an exception or return an error message
		}


		try {
			// Generate a new KeyPair
			logger.info("Generating Key Pair...");
			KeyPair keyPair = KeyManager.createKeyPair();

			// Set default name if none is provided
			if (keyName == null || keyName.isEmpty()) {
				keyName = "GeneratedKey-" + System.currentTimeMillis();
			}

			// Create SigningKey entity and populate fields
			SigningKey signingKey = new SigningKey();
			signingKey.setName(keyName);
			signingKey.setType(keyPair.getPublic().getAlgorithm());
			signingKey.setPublic_key(new String(Hex.encodeHex(keyPair.getPublic().getEncoded())));
			signingKey.setPrivate_key(new String(Hex.encodeHex(keyPair.getPrivate().getEncoded())));

			// Persist the signing key to the database
			logger.info("Persisting the generated signing key...");
			em.getTransaction().begin();
			em.persist(signingKey);
			em.getTransaction().commit();
			logger.info("Signing key generated and persisted successfully.");

			return signingKey;
		} catch (Exception e) {
			logger.error("Error in generating signing key: ", e);
			em.getTransaction().rollback(); // Rollback transaction on failure
		} finally {
			logger.info("Closing persistence context...");
			em.close();
			logger.info("Key generation took " + (System.currentTimeMillis() - start) + "ms.");
			logger.info("================================================================================\n");
		}

		return null;
	}

	// Search signing keys by input
	public List<SigningKey> searchKey(String input) {
		logger.info("Starting signing key search...");

		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();

			// Example JPQL query for search
			String query = "SELECT k FROM SigningKey k WHERE k.name LIKE :search OR k.value LIKE :search";
			List<SigningKey> keys = em.createQuery(query, SigningKey.class).setParameter("search", "%" + input + "%")
					.getResultList();

			em.getTransaction().commit();
			return keys;
		} catch (Exception e) {
			logger.error("Error during search operation: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return new ArrayList<>();
	}

	// Export a signing key to a file
	public String exportSigningKey(int id, String directoryPath) {
		SigningKey key = get(id);
		if (key == null) {
			return "Signing key not found.";
		}

		// Ensure file ends with .key
		String fileName = key.getName() + "_Signing_Key.key";
		File file = new File(directoryPath, fileName);

		// Date format to match license export style
		SimpleDateFormat dateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
		String exportDate = dateFormat.format(new Date());

		try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
			// Write the timestamp at the top of the file
			writer.write("#" + exportDate + "\n");

			// Write private and public keys in the new required format
			writer.write("private=" + key.getPrivate_key() + "\n");
			writer.write("public=" + key.getPublic_key() + "\n");

			logger.info("Signing key exported successfully to {}", file.getAbsolutePath());
			return "Signing key exported successfully to " + file.getAbsolutePath();
		} catch (IOException e) {
			logger.error("Error exporting signing key", e);
			return "Error exporting signing key.";
		}
	}

	// Import a signing key from a file
	public String importSigningKey(String filePath) {
		File file = new File(filePath);
		if (!file.exists()) {
			return "Error: File does not exist.";
		}

		try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
			SigningKey key = new SigningKey();
			String line;

			while ((line = reader.readLine()) != null) {
				if (line.startsWith("#"))
					continue;

				// Parse private and public keys
				if (line.startsWith("private=")) {
					key.setPrivate_key(line.substring("private=".length()).trim());
				}
				if (line.startsWith("public=")) {
					key.setPublic_key(line.substring("public=".length()).trim());
				}
			}

			// Set default name and type since they are not in the file
			key.setName("ImportedKey_" + System.currentTimeMillis());
			key.setType("DSA");

			/*add(key); // Save the imported key to the database
			logger.info("Signing key imported successfully from {}", filePath);
			return "Signing key imported successfully.";*/
			
			 SigningKey saved = add(key);
		        if (saved == null) {
		            logger.warn("Import failed: Duplicate or invalid signing key from {}", filePath);
		            return "Import failed: Duplicate or invalid signing key.";
		        }

		        logger.info("Signing key imported successfully from {}", filePath);
		        return "Signing key imported successfully.";

		} catch (IOException e) {
			logger.error("Error importing signing key from file", e);
			return "Error importing signing key.";
		}
	}

}