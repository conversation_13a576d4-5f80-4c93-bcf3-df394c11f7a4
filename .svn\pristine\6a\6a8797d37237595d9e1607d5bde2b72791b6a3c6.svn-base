package com.accolm.licenseManager.DAO;

import java.util.List;

import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Entities.Contact;

public interface ContactDAO {

	// Create a contact in the License Manager application database.
	Contact createContact(Contact contact);

	// delete contact
	boolean deleteContact(int id);

	// update contact
	Contact updateContact(Contact contact);

	// do a query to get contacts by the companyId
	List<Contact> getContactsByCompanyId(int companyId);

	// list all contacts
	List<Contact> listAllContacts();

}
