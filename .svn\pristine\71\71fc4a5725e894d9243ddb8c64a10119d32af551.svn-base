package com.accolm.licenseManager.DAO;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import com.accolm.licenseManager.Entities.User;
import com.accolm.licenseManager.Utils.LocalEntityManagerFactory;

public class UserDaoImpl implements UserDao {

	Logger logger = LogManager.getLogger(UserDaoImpl.class);

	public UserDaoImpl() {
	}

	@Override
	public User createUser(User user) {

		EntityManager em = LocalEntityManagerFactory.createEntityManager();

		logger.info("Creating a user...");
		User tempUser = null;
		// save
		try {
			// managed transactions by RESOURCE_LOCAL
			EntityTransaction tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.

			tempUser = em.merge(user);

			tx.commit(); // commit the transaction
			logger.info("user created successfully.");
		} catch (Exception e) {
			logger.error("Error creating user: ", e);
		} finally {
			em.close(); // close persistence context.
		}

		return tempUser;

	}

	@Override
	public long getUsersCount() {
		logger.info("Get count of users");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();

		long numberOfUsers = 0;
		EntityTransaction tx = null;

		try {
			tx = em.getTransaction(); // get a transaction to the database.
			tx.begin(); // begin a transaction.

			// Corrected JPQL query with alias
			Query query = em.createQuery("SELECT COUNT(users) FROM User users");

			numberOfUsers = (long) query.getSingleResult(); // Direct cast to long

			tx.commit(); // commit the transaction
			logger.info("Count of users successfully returned");
		} catch (Exception e) {
			if (tx != null && tx.isActive()) {
				tx.rollback(); // rollback in case of error
			}
			logger.error("Error getting count of users ", e);
		} finally {
			if (em.isOpen()) {
				em.close(); // close persistence context
			}
		}

		return numberOfUsers;
	}

	@Override
	public List<User> getAllUsers() {
		logger.info("Retrieving all licenses...");
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			TypedQuery<User> query = em.createQuery("SELECT users FROM User users", User.class);
			return query.getResultList();
		} catch (Exception e) {
			logger.error("Error retrieving all licenses: ", e);
		} finally {
			em.close();
		}
		return new ArrayList<>();
	}

	@Override
	public String deleteUser(String userName) {

		logger.info("Removing user with username: {}", userName);
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			User user = em.find(User.class, userName);
			if (user != null) {
				em.remove(user);
				em.getTransaction().commit();
				logger.info("User removed successfully.");
				return "Status: User removed successfully";
			} else {
				logger.warn("User with userName {} not found.", userName);
			}
		} catch (Exception e) {
			logger.error("Error removing User: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}
		return "Error: User not found or could not be removed.";

	}

	@Override
	public User updateUser(User user) {
		logger.info("Updating User with userName: {}", user.getUsername());
		EntityManager em = LocalEntityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();

			User tempUser = em.find(User.class, user.getUsername());
			if (tempUser != null) {

				tempUser.setCompanyId(user.getCompanyId()); // save the updated companyId
				tempUser.setFirstName(user.getFirstName());
				tempUser.setLastName(user.getLastName());
				
				String theUserPassword = user.getPassword() == null ? tempUser.getPassword() : user.getPassword();
				
				tempUser.setPassword(theUserPassword);
				tempUser.setRole(user.getRole());
				tempUser.setEmail(user.getEmail());

				tempUser.setEnabled(switch (Boolean.toString(user.getEnabled()).toLowerCase()) {
				case "true" -> 1;
				case "false" -> 0;
				default -> 0;
				});
				validateUser(tempUser); // validate the bean.

				em.merge(tempUser); // save the license to the db.
				em.getTransaction().commit(); // commit the transaction
				logger.info("User updated successfully.");
				return tempUser;
			} else {
				logger.warn("User with username {} not found.", user.getUsername());
			}
		} catch (Exception e) {
			logger.error("Error updating user: ", e);
			em.getTransaction().rollback();
		} finally {
			em.close();
		}

		return null;

	}

	private void validateUser(User user) {
		// validate bean.
		ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
		Validator validator = factory.getValidator();

		// do validation.
		Set<ConstraintViolation<User>> beanViolations = validator.validate(user);

		System.err.println("@@@ violating the bean");
		for (ConstraintViolation<User> violation : beanViolations) {

			System.err.println("propertyName:" + violation.getPropertyPath());
			System.err.println("violation message:" + violation.getMessage());
		}

		if (!beanViolations.isEmpty()) {
			throw new ConstraintViolationException(beanViolations);
		}

	}

	public static void main(String[] args) {

	}

}
