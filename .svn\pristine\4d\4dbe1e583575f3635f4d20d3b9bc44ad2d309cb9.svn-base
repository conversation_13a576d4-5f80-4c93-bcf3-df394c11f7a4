package com.accolm.licenseManager.Services;

import java.util.List;

import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Entities.Contact;

public interface ContactService {

	// speak to the ContactDAO method to create contacts.
	Contact createContact(Contact contact);


	// delete contact
	boolean deleteContact(int id);

	// update contact
	Contact updateContact(Contact contact);

	// list contacts by companyId
	List<Contact> getContactsByCompanyId(int id);
	
	// list all contacts
	List<Contact> listAllContacts();

}
