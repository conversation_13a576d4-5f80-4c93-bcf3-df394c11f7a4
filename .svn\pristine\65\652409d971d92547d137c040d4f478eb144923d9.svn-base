package com.accolm.licenseManager.RestInterface;

import java.util.List;

import javax.ws.rs.Consumes;
import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.accolm.licenseManager.Entities.Company;
import com.accolm.licenseManager.Services.CompanyService;
import com.accolm.licenseManager.Services.CompanyServiceImpl;

@Path("companies")
public class CompanyRI {

	// a company service layer for separation of concerns.
	private CompanyService impl = new CompanyServiceImpl();

	// Endpoint to Create a new companies, /api/companies
	@POST
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response createCompany(Company company) {
		Company result = impl.createCompany(company);
		return Response.status(Response.Status.CREATED).entity(result).build();
	}

	// Endpoint to Read Companies, /api/companies.
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response getAllCompanies() {
		List<Company> result = impl.getAllCompanies();
		return Response.status(Response.Status.OK).entity(result).build();
	}

	// count the total number of companies in the list
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Path("count")
	public Response getCountOfCompanies() {
		long result = impl.getCountOfCompanies();
		return Response.status(Response.Status.OK).entity(result).build();
	}

	// contacts endpoint : list companies accordint to id
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Path("{id}")
	public Response listCompanyById(@PathParam("id") int id) {
		Company result = impl.getCompanyById(id);
		return Response.status(Response.Status.OK).entity(result).build();
	}

	// Endpoint to Update an existing companies, /api/companies
	@PUT
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response updateCompany(Company company) {
		Company result = impl.updateCompany(company);
		return Response.status(Response.Status.OK).entity(result).build();
	}

	// 1. endpoint to delete company
	@DELETE
	@Produces(MediaType.APPLICATION_JSON)
	@Path("/delete/{id}")
	public Response deleteCompany(@PathParam("id") int id) {
		boolean isDeleted = impl.deleteCompany(id);
		String message = "";
		if (isDeleted) {
			message = "company sucessfully deleted";
		} else {
			message = "delete company failed.";
		}

		return Response.status(Response.Status.OK).entity(message).build();
	}
	
	
	@GET
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	@Path("/search/{companyName}")
	public Response searchForCompany(@PathParam("companyName") String companyName) {
		List<Company> result =impl.searchCompany(companyName);
		return Response.status(Response.Status.OK).entity(result).build();
	} 


}
